import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from '../env/env';
import { LoaderService } from './loader.service';
import { Observable } from 'rxjs';
import data from '../../assets/Json/box.json';
import { ResponseData } from '../_models/common.model';
import { ToastrService } from 'ngx-toastr';
import { firstValueFrom } from 'rxjs';
import {
  BackendResponse,
  ColorPalettes,
  PlotResponseAll,
  PlotStyle,
  PlotStyles,
  UserPlot,
} from '../_models/visual-data/visual-data.model';
import { GridsterItem } from 'angular-gridster2';
import {
  Folder,
  PatchedUserPlotRequest,
  PlotInfoResponse,
  PlotTypesResponse,
  SetFavoritePlotResponse,
  UserPlotRequest,
} from '../_models/visual-data/plot.model';
import { CreatePlotPayload } from '../_models/visual-data/explore-view.model';

export interface PlotLayoutPayload extends Partial<GridsterItem> {
  plot_id: number;
}

@Injectable({
  providedIn: 'root',
})
export class PlotService {
  private jsonData: string;

  constructor(
    private http: HttpClient,
    private loader: LoaderService,
    private toastrService: ToastrService,
  ) {
    //TODO extract Type
    this.jsonData = JSON.stringify(data);
  }

  private getHeaders(): HttpHeaders {
    const token = localStorage.getItem('access_token');
    return new HttpHeaders({
      Authorization: `Bearer ${token || ''}`,
      'Content-Type': 'application/json',
    });
  }

  setNewPlot(
    file_id: number,
    payload: CreatePlotPayload,
  ): Observable<BackendResponse<UserPlotRequest>> {
    return this.http.post<BackendResponse<UserPlotRequest>>(
      `${environment.apiUrl}visualization/user-plot/?file_id=${file_id}&store_in_SelectedPlot=True&show_fig=True`,
      payload,
      {
        headers: this.getHeaders(),
      },
    );
  }

  getCustomPlots(
    project_id: number,
    payload: string,
  ): Observable<BackendResponse<PlotResponseAll>> {
    return this.http.get<BackendResponse<PlotResponseAll>>(
      `${environment.apiUrl}visualization/user-plot?project_id=${project_id}&show_fig=True&${payload}`,
      {
        headers: this.getHeaders(),
      },
    );
  }

  getAllPlots(): Observable<BackendResponse<PlotTypesResponse>> {
    const headers = this.getHeaders();
    return this.http.get<BackendResponse<PlotTypesResponse>>(
      `${environment.apiUrl}visualization/Plot/`,
      {
        headers,
      },
    );
  }

  plotFileList(project_id: number): Observable<BackendResponse<Folder[]>> {
    const headers = this.getHeaders();
    return this.http.get<BackendResponse<Folder[]>>(
      `${environment.apiUrl}projects/projects/${project_id}/files/filter/?search_files=true&file_type=non_image`,
      { headers },
    );
  }

  getPlotInfo(
    plot_name: string | null,
  ): Observable<BackendResponse<PlotInfoResponse>> {
    const headers = this.getHeaders();
    return this.http.get<BackendResponse<PlotInfoResponse>>(
      `${environment.apiUrl}visualization/Plot/${plot_name}/`,
      { headers },
    );
  }

  getPlotStyles(project_id: number): Observable<ResponseData<PlotStyles>> {
    const headers = this.getHeaders();
    return this.http.get<ResponseData<PlotStyles>>(
      `${environment.apiUrl}visualization/PlotStyle/${project_id}/`,
      { headers },
    );
  }

  getPlotStyleName(
    project_id: number,
    payload: object,
  ): Observable<ResponseData<PlotStyle>> {
    const headers = this.getHeaders();
    return this.http.post<ResponseData<PlotStyle>>(
      `${environment.apiUrl}visualization/PlotStyle/${project_id}/`,
      payload,
      { headers },
    );
  }

  getColorPalette(): Observable<ResponseData<ColorPalettes>> {
    const headers = this.getHeaders();
    return this.http.get<ResponseData<ColorPalettes>>(
      `${environment.apiUrl}visualization/ColorPalette/`,
      { headers },
    );
  }

  updateColorPalette(
    colour_palette_id: number,
    payload: unknown,
  ): Observable<unknown> {
    return this.http.put<unknown>(
      `${environment.apiUrl}visualization/ColorPalette/${colour_palette_id}/`,
      payload,
      {
        headers: this.getHeaders(),
      },
    );
  }

  changeSettings(plotStyleId: number, payload: PlotStyle): Observable<unknown> {
    console.log('send put');
    return this.http.put<unknown>(
      `${environment.apiUrl}visualization/PlotStyles/${plotStyleId}/`,
      payload,
      {
        headers: this.getHeaders(),
      },
    );
  }

  favPlot(
    plot_id: number,
    favorite: boolean,
  ): Observable<BackendResponse<SetFavoritePlotResponse>> {
    const headers = this.getHeaders(); // Fetch your headers

    return this.http.post<BackendResponse<SetFavoritePlotResponse>>(
      `${environment.apiUrl}visualization/UserPlot/${plot_id}/favorite/?favorite=${favorite}`,
      {},
      { headers },
    );
  }

  //TODO unknown for now need it from backend
  removePlot(plot_id: number): Observable<BackendResponse<unknown>> {
    const headers = this.getHeaders(); // Fetch your headers
    return this.http.delete<BackendResponse<unknown>>(
      `${environment.apiUrl}visualization/UserPlot/${plot_id}/`,
      { headers },
    );
  }

  // filter data
  UserPlotAssociateFilter(
    plot_id: number,
    filters: string,
  ): Observable<BackendResponse<UserPlotRequest>> {
    const headers = this.getHeaders(); // Fetch your headers
    return this.http.post<BackendResponse<UserPlotRequest>>(
      `${environment.apiUrl}visualization/user-plot/${plot_id}/associate-filters/`,
      filters,
      { headers },
    );
  }

  RemoveUserPlotAssociateFilter(plot_id: number): Observable<unknown> {
    const headers = this.getHeaders(); // Fetch your headers
    return this.http.delete<unknown>(
      `${environment.apiUrl}visualization/user-plot/${plot_id}/associate-filters/`,
      { headers },
    );
  }

  getPlotEditData(plot_id: number): Observable<unknown> {
    const headers = this.getHeaders(); // Fetch your headers
    return this.http.put<unknown>(
      `${environment.apiUrl}visualization/UserPlot/${plot_id}/`,
      {},
      { headers },
    );
  }

  getPlotEditInfo(plot_id: number): Observable<BackendResponse<UserPlot>> {
    const headers = this.getHeaders(); // Fetch your headers
    return this.http.get<BackendResponse<UserPlot>>(
      `${environment.apiUrl}visualization/UserPlot/${plot_id}/`,
      { headers },
    );
  }

  getPlotDescription(
    plot_id: number,
  ): Observable<BackendResponse<UserPlotRequest>> {
    const headers = this.getHeaders(); // Fetch your headers

    return this.http.post<BackendResponse<UserPlotRequest>>(
      `${environment.apiUrl}visualization/UserPlot/${plot_id}/description/`,
      {},
      { headers },
    );
  }

  getJsonData(): Observable<unknown> {
    return this.http.get<unknown>(this.jsonData);
  }

  savePlotLayoutData(
    project_id: number,
    layout: PlotLayoutPayload[],
  ): Observable<BackendResponse<PatchedUserPlotRequest>> {
    return this.http.patch<BackendResponse<PatchedUserPlotRequest>>(
      `${environment.apiUrl}visualization/user-plot/display-layout/?project_id=${project_id}`,
      layout,
      {
        headers: this.getHeaders(),
      },
    );
  }

  // Fetch and process plot types
  async fetchAllPlotTypes(): Promise<PlotTypesResponse | null> {
    try {
      const res = await firstValueFrom(this.getAllPlots());
      if (res.status === 'success') {
        const processedPlotTypes = {
          ...res.data,
          CompleteList: [...res.data.Visualise, ...res.data.Explain],
        };
        return processedPlotTypes;
      } else {
        this.toastrService.error('Failed to fetch plot types');
        return null;
      }
    } catch (error) {
      console.error('Error while fetching plot types:', error);
      return null;
    }
  }
}
