<app-loader [loading]="loading"></app-loader>

<div class="w-full h-full flex flex-col" *ngIf="isTableFile">
  <app-file-header
    class=""
    [filename]="filename"
    [filter_active]="filter_active"
    (sectionChanged)="handleSectionChange($event)"
    (sectionTypeChanged)="handleTypeChange($event)"
    (sectionProcessedChanged)="handleProcessedChange($event)"
    (buttonClicked)="handleRandomFileDta()"
    (filterDataClicked)="filterData()"
    (AddColumnClicked)="openAddColumnModal()"></app-file-header>

  <!-- Table Section -->
  <div
    class="overflow-auto mt-6 rounded-2xl shadow flex-1"
    id="light-table"
    *ngIf="section === 'data'">
    <table mat-table [dataSource]="dataSource">
      <ng-container
        *ngFor="let column of displayedColumns"
        [matColumnDef]="column">
        <th
          mat-header-cell
          *matHeaderCellDef
          class="relative group dark:text-white">
          <div class="flex justify-between items-center ml-10">
            <span class="whitespace-nowrap">
              {{ replaceUnderscoreWithSpace(capitalizeFirstLetter(column)) }}
            </span>
            <button
              mat-icon-button
              [matMenuTriggerFor]="menu"
              aria-label="Menu"
              class="ml-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 ease-in-out">
              <mat-icon>more_vert</mat-icon>
            </button>
          </div>
        </th>

        <td mat-cell *matCellDef="let element" class="dark:text-white">
          {{ element[column] }}
        </td>
        <mat-menu #menu="matMenu" class="w-60 h-76 rounded-lg !bg-white">
          <button mat-menu-item (click)="openModal(column)" class="hidden">
            <span class="flex items-center">
              <mat-icon class="mr-2 text-gray-500">data_array</mat-icon>
              <span class="text-[#82868e]">Change data type</span>
            </span>
          </button>

          <button
            mat-menu-item
            (click)="selectAsId(column)"
            *ngIf="applicableIdColumns.includes(column)">
            <span class="flex items-center">
              <mat-icon class="mr-2 text-gray-500"
                >radio_button_checked</mat-icon
              >
              <span class="text-[#82868e]">Select as Id</span>
            </span>
          </button>

          <button mat-menu-item (click)="filterData(column)">
            <span class="flex items-center">
              <mat-icon class="mr-2 text-gray-500">filter_list</mat-icon>
              <span class="text-[#82868e]">Filter Data</span>
            </span>
          </button>

          <button mat-menu-item (click)="sortData(column, 'ascending')">
            <span class="flex items-center">
              <mat-icon class="mr-2 text-gray-500">sort_by_alpha</mat-icon>
              <span class="text-[#82868e]">Sort ascending</span>
            </span>
          </button>

          <button mat-menu-item (click)="sortData(column, 'descending')">
            <span class="flex items-center">
              <mat-icon class="mr-2 text-gray-500">sort_by_alpha</mat-icon>
              <span class="text-[#82868e]">Sort descending</span>
            </span>
          </button>

          <button mat-menu-item (click)="addColumn(column, 'before')">
            <span class="flex items-center">
              <mat-icon class="mr-2 text-gray-500">arrow_left</mat-icon>
              <span class="text-[#82868e]">Add column before</span>
            </span>
          </button>

          <button mat-menu-item (click)="addColumn(column, 'after')">
            <span class="flex items-center">
              <mat-icon class="mr-2 text-gray-500">arrow_right</mat-icon>
              <span class="text-[#82868e]">Add column after</span>
            </span>
          </button>

          <button mat-menu-item (click)="removeColumn(column)">
            <span class="flex items-center">
              <mat-icon class="mr-2">delete_outline</mat-icon>
              <span class="text-[#82868e]">Remove column</span>
            </span>
          </button>
        </mat-menu>
      </ng-container>

      <!-- Header and Row Declarations -->
      <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
      <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
    </table>
  </div>
  <div
    *ngIf="section === 'statistics'"
    class="overflow-scroll mt-6 rounded-2xl shadow flex-1">
    <table class="min-w-full table-auto" id="light-data-table">
      <thead>
        <tr>
          <th class="px-4 py-2 dark:text-white">Metric</th>
          <th
            *ngFor="let keyValue of statisticsData | keyvalue"
            class="px-4 py-2 font-bold dark:text-white">
            {{ capitalizeFirstLetter(keyValue.key) }}
          </th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let header of getTableHeaders()">
          <td
            class="border px-4 py-2 font-extrabold text-black dark:text-white">
            {{ capitalizeFirstLetter(header) }}
          </td>
          <td
            *ngFor="let keyValue of statisticsData | keyvalue"
            class="border px-4 py-2 dark:text-white">
            {{ keyValue.value[header] }}
          </td>
        </tr>
      </tbody>
    </table>
  </div>

  <!-- Footer-section -->
  <div class="grid grid-cols-[95%_5%] mt-4">
    <div class="flex items-center justify-center">
      <mat-paginator
        class="border border-gray-300 rounded-lg shadow-sm"
        [length]="totalPages"
        [pageSize]="pageSize"
        [showFirstLastButtons]="true"
        [hidePageSize]="true"
        (page)="onPageChange($event)">
      </mat-paginator>
    </div>
    <div class="flex items-center justify-end">
      <button
        mat-icon-button
        (click)="downloadData()"
        class="bg-gray-200 rounded-lg hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 transition flex items-center justify-center">
        <mat-icon class="text-gray-600 dark:text-gray-300"
          >file_download</mat-icon
        >
      </button>
    </div>
  </div>

  <div
    *ngIf="isModalOpen"
    class="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-50">
    <div class="bg-[#F1F3F9] rounded-xl shadow-lg p-4 relative w-[40%]">
      <div class="flex justify-between items-center p-2">
        <h6 class="font-medium">Change Data Type - {{ selectedColumn }}</h6>
        <button
          mat-icon-button
          (click)="closeModal()"
          class="text-gray-400 hover:text-gray-600">
          <mat-icon>close</mat-icon>
        </button>
      </div>
      <div class="border-t-[1px] opacity-40 w-full border-gray-300"></div>

      <div class="mt-2">
        <div class="flex justify-between">
          <p>No. of Columns</p>
          <p class="flex items-center gap-1">
            Data type
            <mat-icon class="text-blue-500">info</mat-icon>
          </p>
          <p class="mr-2">Id</p>
        </div>
        <form class="flex justify-between items-center gap-4 p-1 w-full">
          <div class="flex justify-start">
            <input
              placeholder="Enter Value"
              class="outline-none bg-transparent border px-3 py-2 shadow-sm h-12 rounded-md"
              readonly />
          </div>

          <div class="w-full">
            <mat-select
              class="outline-none bg-white border p-3 shadow-sm h-12 rounded-md w-full">
              <mat-option value="" disabled selected
                >Select Operator
              </mat-option>
              <mat-option value="equals">Equals</mat-option>
              <mat-option value="not-equals">Not Equals</mat-option>
            </mat-select>
          </div>

          <div class="flex justify-end items-center">
            <mat-radio-button></mat-radio-button>
          </div>
        </form>
      </div>

      <div class="mt-6 flex justify-start space-x-4">
        <button mat-flat-button class="bg-btn-color" (click)="saveChanges()">
          Save Changes
        </button>
        <button
          mat-button
          class="bg-white text-txt-color hover:bg-gray-200"
          (click)="discardChanges()">
          <mat-icon>restart_alt</mat-icon>
          Discard Changes
        </button>
      </div>
    </div>
  </div>
</div>
<!-- FilterData modal -->

<div
  *ngIf="isFilterDataModalOpen"
  class="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-50 min-w-[700px] min-h-[200px]">
  <div
    class="mat-dialog rounded-xl shadow-lg p-4 relative w-auto h-auto min-w-[55vw] min-h-[40vh] bg-white dark:text-white">
    <div class="flex justify-between items-center object-center p-4 pl-2">
      <div class="flex items-center justify-center">
        <h3 class="m-0">Filter Data</h3>
        <button mat-icon-button class="">
          <mat-icon>info_outline</mat-icon>
        </button>
      </div>

      <button
        mat-icon-button
        (click)="closeFilterDataModal()"
        class="text-gray-400 hover:text-gray-600">
        <mat-icon>close</mat-icon>
      </button>
    </div>
    <div class="border-t-[1px] opacity-40 w-full border-gray-300 mb-4"></div>
    <div class="flex flex-row items-center mt-4">
      <div class="w-[45%] pr-4 font-medium text-base">Column</div>
      <div class="w-[25%] pr-4 font-medium text-base">Operator</div>
      <div class="w-[25%] pr-4 font-medium text-base">Value</div>
      <div class="w-[5%]"></div>
    </div>

    <!-- Always include the first row -->
    <div class="flex flex-row items-center mt-4">
      <div class="w-[45%] pr-4">
        <mat-select
          class="outline-none flex-grow border px-3 py-2 shadow-sm h-12 rounded-md"
          [(ngModel)]="filterDataRows[0].selectedColumn"
          (selectionChange)="onColumnChange($event, filterDataRows[0])"
          placeholder="Choose Column">
          <mat-optgroup label="Categories">
            <mat-option *ngFor="let category of categories" [value]="category">
              {{ category }}
            </mat-option>
          </mat-optgroup>

          <mat-optgroup label="Numerical">
            <mat-option *ngFor="let number of numerical" [value]="number">
              {{ number }}
            </mat-option>
          </mat-optgroup>
        </mat-select>
      </div>
      <div class="w-[25%] pr-4">
        <mat-select
          [(ngModel)]="filterDataRows[0].selectedOperator"
          class="outline-none border px-3 py-2 shadow-sm h-12 rounded-md"
          (selectionChange)="onOperatorChange($event, filterDataRows[0])"
          placeholder="Select Operator">
          <mat-select-trigger>
            <ng-container
              *ngTemplateOutlet="
                operationIconTemplate;
                context: { operation: filterDataRows[0].selectedOperator }
              ">
            </ng-container>
          </mat-select-trigger>
          <mat-option
            *ngFor="let operator of filterDataRows[0].availableOperators"
            [value]="operator">
            <ng-container
              *ngTemplateOutlet="
                operationIconTemplate;
                context: { operation: operator }
              ">
            </ng-container>
          </mat-option>
        </mat-select>
      </div>
      <div class="w-[25%] pr-4">
        <ng-container
          *ngIf="
            filterDataRows[0].columnType === 'Categories';
            else numericalInput
          ">
          <div class="relative flex">
            <input
              type="text"
              class="outline-none bg-transparent min-w-20 border px-3 py-2 shadow-sm h-12 rounded-md"
              [(ngModel)]="filterDataRows[0].selectedValue"
              (focus)="showDropdown = true"
              (blur)="hideDropdownWithDelay()"
              placeholder="Select or type a value" />

            <ul
              *ngIf="showDropdown"
              class="absolute border border-gray-300 shadow-md rounded-md mt-1 w-full max-h-40 overflow-auto stroke-btn z-10 dark:bg-[#181C20]">
              <li
                *ngFor="let option of filterDataRows[0].availableValues"
                class="px-4 py-2 hover:bg-gray-100 cursor-pointer dark:bg-[#181C20] dark:hover:bg-[#212529]"
                (click)="selectOption(option)"
                (keydown.enter)="selectOption(option)"
                tabindex="0">
                {{ option }}
              </li>
            </ul>
          </div>
        </ng-container>

        <ng-template #numericalInput>
          <ng-container
            *ngIf="filterDataRows[0].isBetweenOperator; else singleInput">
            <div class="flex gap-1">
              <input
                [placeholder]="'Min: ' + filterDataRows[0].minPlaceholder"
                class="outline-none flex-grow-2 border px-3 py-2 shadow-sm h-12 rounded-md min-w-16"
                [(ngModel)]="filterDataRows[0].minValue" />
              <input
                [placeholder]="'Max: ' + filterDataRows[0].maxPlaceholder"
                class="outline-none flex-grow-2 border px-3 py-2 shadow-sm h-12 rounded-md min-w-16"
                [(ngModel)]="filterDataRows[0].maxValue" />
            </div>
          </ng-container>
          <ng-template #singleInput>
            <div class="flex">
              <input
                [placeholder]="
                  filterDataRows[0].selectedColumn
                    ? 'From ' +
                      filterDataRows[0].minPlaceholder +
                      ' to ' +
                      filterDataRows[0].maxPlaceholder
                    : 'Select a value'
                "
                class="outline-none bg-transparent min-w-20 border px-3 py-2 shadow-sm h-12 rounded-md"
                [(ngModel)]="filterDataRows[0].singleValue" />
            </div>
          </ng-template>
        </ng-template>
      </div>
      <div class="w-[5%]"></div>
    </div>

    <!-- Additional rows can be added here -->
    <div
      *ngFor="let row of filterDataRows.slice(1); let i = index"
      class="flex flex-row items-center mt-4">
      <div class="w-[45%] pr-4 flex">
        <mat-select
          [(ngModel)]="row.logicOperator"
          class="outline-none border px-3 py-2 shadow-sm h-12 rounded-md max-w-32 mr-2"
          placeholder="Select Logic">
          <mat-option value="AND">AND</mat-option>
          <mat-option value="OR">OR</mat-option>
        </mat-select>

        <mat-select
          [(ngModel)]="row.selectedColumn"
          class="outline-none flex-grow border px-3 py-2 shadow-sm w-auto h-12 rounded-md"
          (selectionChange)="onColumnChange($event, row)"
          placeholder="Choose Column">
          <mat-optgroup label="Categories">
            <mat-option *ngFor="let category of categories" [value]="category">
              {{ category }}
            </mat-option>
          </mat-optgroup>
          <mat-optgroup label="Numerical">
            <mat-option *ngFor="let number of numerical" [value]="number">
              {{ number }}
            </mat-option>
          </mat-optgroup>
        </mat-select>
      </div>
      <div class="w-[25%] pr-4">
        <mat-select
          [(ngModel)]="row.selectedOperator"
          class="outline-none border px-3 py-2 shadow-sm h-12 rounded-md"
          (selectionChange)="onOperatorChange($event, row)"
          placeholder="Select Operator">
          <mat-select-trigger>
            <ng-container
              *ngTemplateOutlet="
                operationIconTemplate;
                context: { operation: row.selectedOperator }
              ">
            </ng-container>
          </mat-select-trigger>
          <mat-option
            *ngFor="let operator of row.availableOperators"
            [value]="operator">
            <ng-container
              *ngTemplateOutlet="
                operationIconTemplate;
                context: { operation: operator }
              ">
            </ng-container>
          </mat-option>
        </mat-select>
      </div>
      <div class="w-[25%] pr-4">
        <ng-container
          *ngIf="row.columnType === 'Categories'; else numericalInput">
          <div class="relative flex">
            <input
              type="text"
              class="outline-none bg-transparent min-w-20 border px-3 py-2 shadow-sm h-12 rounded-md"
              [(ngModel)]="row.selectedValue"
              (focus)="row.showValues = true"
              (blur)="hideDropdownWithDelay()"
              placeholder="Select or type a value" />

            <ul
              *ngIf="row.showValues"
              class="absolute border border-gray-300 shadow-md rounded-md mt-1 w-full max-h-40 overflow-auto stroke-btn z-10">
              <li
                *ngFor="let option of row.availableValues"
                class="px-4 py-2 hover:bg-gray-100 cursor-pointer"
                (click)="selectValue(option, row)"
                (keyup.enter)="selectValue(option, row)"
                tabindex="0">
                {{ option }}
              </li>
            </ul>
          </div>
        </ng-container>

        <ng-template #numericalInput>
          <ng-container *ngIf="row.isBetweenOperator; else singleInput">
            <div class="flex gap-1">
              <input
                [placeholder]="'Min: ' + row.minPlaceholder"
                class="outline-none flex-grow-2 border px-3 py-2 shadow-sm h-12 rounded-md min-w-16"
                [(ngModel)]="row.minValue" />
              <input
                [placeholder]="'Max: ' + row.maxPlaceholder"
                class="outline-none flex-grow-2 border px-3 py-2 shadow-sm h-12 rounded-md min-w-16"
                [(ngModel)]="row.maxValue" />
            </div>
          </ng-container>
          <ng-template #singleInput>
            <div class="flex">
              <input
                [placeholder]="
                  row.selectedColumn
                    ? 'From ' + row.minPlaceholder + ' to ' + row.maxPlaceholder
                    : 'Select a value'
                "
                class="outline-none bg-transparent flex-grow-2 min-w-20 border px-3 py-2 shadow-sm h-12 rounded-md"
                [(ngModel)]="row.singleValue" />
            </div>
          </ng-template>
        </ng-template>
      </div>
      <div class="w-[5%]">
        <button
          class="w-8 flex justify-center items-center"
          (click)="removeRow(i + 1)">
          <mat-icon>delete_outline</mat-icon>
        </button>
      </div>
    </div>

    <div class="mt-3">
      <!-- Button to add more rows -->
      <button
        class="border-[1px] rounded-full flex items-center !border-gray-300 space-x-1 p-3 !bg-transparent !text-txt-color"
        (click)="addRow()">
        <mat-icon>add</mat-icon>
      </button>
    </div>

    <div class="flex justify-start mt-4 gap-2">
      <button
        mat-flat-button
        [disabled]="!isFormValid()"
        (click)="filterDataSave()">
        Save
      </button>
      <button
        mat-button
        class="default-filter text-txt-color"
        (click)="removeFilter()">
        <mat-icon>restart_alt</mat-icon>
        Remove Filter
      </button>
    </div>
  </div>
</div>
<!-- Add Column Modal -->
<div
  *ngIf="isAddColumnModalOpen"
  class="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-50">
  <div
    class="mat-dialog rounded-xl shadow-lg p-4 relative w-auto h-auto min-w-[40vw] min-h-[40vh] bg-white">
    <div class="flex justify-between items-center object-center p-4 pl-2">
      <div class="flex items-center justify-center">
        <h3 class="m-0 dark:text-white">Add Column</h3>
        <!-- In your component template -->
        <button
          mat-icon-button
          [matTooltip]="tooltipMessage"
          matTooltipPosition="right"
          matTooltipClass="custom-tooltip">
          <mat-icon>info_outline</mat-icon>
        </button>
      </div>

      <button
        mat-icon-button
        (click)="closeAddColumnModal()"
        class="text-gray-400 hover:text-gray-600 dark:text-white">
        <mat-icon>close</mat-icon>
      </button>
    </div>
    <div class="border-t-[1px] opacity-40 w-full border-gray-300"></div>

    <div
      class="overflow-hidden hover:overflow-y-auto max-h-[400px] dark:text-white">
      <form class="flex flex-col" #form="ngForm">
        <div *ngFor="let row of dynamicAddColumn; let i = index">
          <div class="flex w-full my-4 px-2">
            <div class="w-full">
              <label for="style" class="flex flex-col font-medium text-base"
                >New Column {{ i + 1 }}</label
              >
              <input
                [(ngModel)]="row.metric_name"
                name="metric_name{{ i }}"
                placeholder="New Column Name"
                class="outline-none bg-transparent border px-3 py-2 mt-3 shadow-sm w-full h-12 rounded-md"
                required
                #metricName="ngModel" />
              @if (
                (metricName.touched || isAddColumnFormSubmitted) &&
                metricName.invalid
              ) {
                <div class="text-red-500 text-sm mt-1">
                  This field may not be blank.
                </div>
              }
            </div>
          </div>

          <div class="flex w-full mb-4 px-2 gap-4">
            <div class="w-full">
              <label for="style" class="flex flex-col font-medium text-base"
                >Column 1</label
              >
              <mat-select
                class="outline-none border px-3 py-2 shadow-sm w-full h-12 rounded-md mt-3"
                [(ngModel)]="row.col1"
                name="col1{{ i }}"
                placeholder="Choose Column"
                required
                #col1="ngModel">
                <mat-option *ngFor="let number of numerical" [value]="number">
                  {{ number }}
                </mat-option>
              </mat-select>
              @if ((col1.touched || isAddColumnFormSubmitted) && col1.invalid) {
                <div class="text-red-500 text-sm mt-1">
                  This field may not be blank.
                </div>
              }
            </div>

            <div class="w-full">
              <label for="style" class="flex flex-col font-medium text-base"
                >Operator</label
              >
              <mat-select
                class="outline-none border px-3 py-2 h-12 w-full rounded-md mt-3"
                [(ngModel)]="row.operation"
                name="operation{{ i }}"
                placeholder="Choose Operator"
                required
                #operation="ngModel">
                <mat-select-trigger>
                  <ng-container
                    *ngTemplateOutlet="
                      operationIconTemplate;
                      context: { operation: row.operation }
                    ">
                  </ng-container>
                </mat-select-trigger>

                @for (operation of addColumnOperations; track operation) {
                  <mat-option [value]="operation">
                    <ng-container
                      *ngTemplateOutlet="
                        operationIconTemplate;
                        context: { operation: operation }
                      ">
                    </ng-container>
                  </mat-option>
                }
              </mat-select>
              @if (
                (operation.touched || isAddColumnFormSubmitted) &&
                operation.invalid
              ) {
                <div class="text-red-500 text-sm mt-1">
                  This field may not be blank.
                </div>
              }
            </div>

            <div class="w-full">
              <label for="style" class="flex flex-col font-medium text-base"
                >Column 2
              </label>
              <mat-select
                class="outline-none border px-3 py-2 shadow-sm w-full h-12 rounded-md mt-3"
                [(ngModel)]="row.col2_or_scalar"
                name="col2_or_scalar{{ i }}"
                placeholder="Choose Column"
                required
                #col2="ngModel">
                <mat-option *ngFor="let number of numerical" [value]="number">
                  {{ number }}
                </mat-option>
              </mat-select>
              @if ((col2.touched || isAddColumnFormSubmitted) && col2.invalid) {
                <div class="text-red-500 text-sm mt-1">
                  This field may not be blank.
                </div>
              }
            </div>
          </div>
        </div>
      </form>

      <button
        (click)="addAnotherColumn()"
        class="border-[1px] rounded-full flex items-center !border-gray-300 space-x-1 p-2 !bg-transparent !text-txt-color">
        <mat-icon>add</mat-icon>
      </button>
    </div>

    <div class="flex justify-start mt-9">
      <button mat-flat-button (click)="addModalSave(form)">Save</button>
    </div>
  </div>
</div>

<app-images
  *ngIf="isTableFile === false"
  [fileID]="fileID"
  [imageData]="imageDataOutput"></app-images>

<ng-template #operationIconTemplate let-operation="operation">
  <div class="flex items-center gap-2">
    @if (operationsIconMapping[operation]) {
      <div
        class="h-8 w-8 rounded-full bg-[#6750A414] flex items-center justify-center font-normal text-lg">
        {{ operationsIconMapping[operation] }}
      </div>
    }
    <span>{{ operation }}</span>
  </div>
</ng-template>
