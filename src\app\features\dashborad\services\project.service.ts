import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from '../../../env/env';
import {
  ColumnNameType,
  Project,
  ProjectList,
  ProjectPayload,
  SignedUrlResponse,
  UploadedFile,
} from '../models/project.model';
import { LoaderService } from './../../../services/loader.service';
import {
  Message,
  PaginationResult,
  ResponseData,
  SearchOptions,
} from '../../../_models/common.model';
import { map, Observable, shareReplay } from 'rxjs';

type FileRow = Record<string, string | number | boolean>;

//TODO GET /files/process/${file_id}/
export interface ProcessFileData {
  data: {
    message: string;
    data: FileRow[];
    columns: [];
    processed_file_id: number;
  };
}

interface DataField {
  name: string;
  type: 'integer' | 'string' | 'float';
  options: string[] | null;
}

@Injectable({
  providedIn: 'root',
})
export class ProjectService {
  private projectsUrl = environment.apiUrl + 'projects';
  constructor(
    private http: HttpClient,
    private loader: LoaderService,
  ) {}

  private getHeaders(): HttpHeaders {
    const token = localStorage.getItem('access_token');
    return new HttpHeaders({
      Authorization: `Bearer ${token || ''}`,
      'Content-Type': 'application/json',
    });
  }

  /**
   * Add a new project
   * @param newProject - The project payload containing title and description
   * @returns Observable<ResponseData<Project>>
   */
  createProject(data: ProjectPayload): Observable<ResponseData<Project>> {
    return this.http.post<ResponseData<Project>>(`${this.projectsUrl}/`, data, {
      headers: this.getHeaders(),
    });
  }

  deleteProject(projectId: number): Observable<Message> {
    return this.http.delete<Message>(`${this.projectsUrl}/${projectId}/`, {
      headers: this.getHeaders(),
    });
  }

  editProject(
    data: ProjectPayload,
    projectId: number,
  ): Observable<ResponseData<Project>> {
    return this.http.put<ResponseData<Project>>(
      `${this.projectsUrl}/${projectId}/`,
      data,
      { headers: this.getHeaders() },
    );
  }

  getProjectsBySearch(
    options?: SearchOptions,
  ): Observable<PaginationResult<Project[]>> {
    const params = [
      { key: 'search_projects', value: 'true' },
      { key: 'title_contains', value: options?.title },
      { key: 'description_contains', value: options?.desc },
      { key: 'min_created_at', value: options?.minDate },
      { key: 'max_created_at', value: options?.maxDate },
    ];

    const optionsString = params
      .filter(param => param.value)
      .map(param => `${param.key}=${param.value}`)
      .join('&');

    const finalString = optionsString ? `?${optionsString}` : '';
    return this.http.get<PaginationResult<Project[]>>(
      `${this.projectsUrl}/projects/${finalString}`,
      { headers: this.getHeaders() },
    );
  }

  getProjectsUrl(): string {
    return this.projectsUrl;
  }

  getProjectsList(): Observable<ResponseData<ProjectList[]>> {
    const headers = this.getHeaders();
    return this.http
      .get<
        ResponseData<ProjectList[]>
      >(`${this.projectsUrl}/projects/`, { headers })
      .pipe(shareReplay(1));
  }

  getProjects(pageSize: number): Observable<ProjectList> {
    const params = new HttpParams().set('page_size', pageSize.toString());
    const headers = this.getHeaders(); // No need to pass page_size in headers
    return this.http
      .get<ProjectList>(`${this.projectsUrl}/`, { headers, params })
      .pipe(
        map((response: ProjectList) => response),
        shareReplay(1),
      );
  }

  getProjectsByUrl(url: string): Observable<ProjectList> {
    const headers = this.getHeaders();
    return this.http.get<ProjectList>(url, { headers });
  }

  /* files api's */
  private filesUrl = `${environment.apiUrl}files`;

  saveSessionVariables(
    file_id: number,
    project_id: number,
    ID_column: string,
    target_column: string,
    columns_passed: string[],
  ): Observable<ResponseData<string>> {
    return this.http.post<ResponseData<string>>(
      `${this.filesUrl}/StoreAPIRequestInfo/?file_id=${file_id}&project_id=${project_id}&ID_column=${ID_column}&target_column=${target_column}&columns_passed=${columns_passed}`,
      {},
    );
  }

  generateSignedUrl(
    fileName: string,
    folder_id: number,
  ): Observable<SignedUrlResponse> {
    this.loader.isCommonLoaderRequired = false;
    return this.http.post<SignedUrlResponse>(
      `${this.filesUrl}/files/upload-url/${folder_id}/`,
      { filename: fileName },
    );
  }

  uploadFile(url: string, file: Blob): Observable<Blob> {
    this.loader.isCommonLoaderRequired = false;
    return this.http.put<Blob>(url, file);
  }

  getFileStatus(
    filename: string,
    folder_id: number,
  ): Observable<ResponseData<{ id: string }>> {
    this.loader.isCommonLoaderRequired = false;
    return this.http.post<ResponseData<{ id: string }>>(
      `${this.filesUrl}/files/upload-status/${folder_id}/`,
      {
        filename: filename,
      },
    );
  }

  getUploadedFilesListByProject(id: number): Observable<UploadedFile[]> {
    return this.http
      .get<UploadedFile[]>(`${this.filesUrl}/files/project/${id}`)
      .pipe(shareReplay(1));
  }

  processUploadedFile(file_id: number): Observable<ProcessFileData> {
    this.loader.isCommonLoaderRequired = false;
    return this.http.post<ProcessFileData>(
      `${this.filesUrl}/files/process/${file_id}/`,
      {},
    );
  }

  deleteUploadedFile(file_id: number): Observable<Message> {
    return this.http.delete<Message>(
      `${this.filesUrl}/files/delete/?file_id=${file_id}`,
    );
  }

  getColumnTypes(file_id: number | undefined): Observable<ColumnNameType> {
    return this.http.get<ColumnNameType>(
      `${this.filesUrl}/ColumnInfo/${file_id}/`,
    );
  }

  editColumnTypes(
    file_id: number | undefined,
    data: DataField[],
  ): Observable<Message> {
    return this.http.put<Message>(
      `${this.filesUrl}/ColumnInfo/${file_id}/`,
      data,
    );
  }

  storeIdColumn(idColumn: string, fileId: number): Observable<Message> {
    return this.http.post<Message>(
      `${this.filesUrl}/SaveIDColumn/${fileId}/?${idColumn ? `&ID_column=${idColumn}` : ''}`,
      null,
    );
  }
}
