import { Component } from '@angular/core';

import { MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';

@Component({
  selector: 'app-info-modal',
  imports: [MatDialogModule, MatButtonModule, MatIconModule],
  templateUrl: './info-modal.component.html',
  styleUrl: './info-modal.component.css',
})
export class InfoModalComponent {
  constructor(private popupRef: MatDialogRef<InfoModalComponent>) {}

  closeModal() {
    this.popupRef.close();
  }
}
