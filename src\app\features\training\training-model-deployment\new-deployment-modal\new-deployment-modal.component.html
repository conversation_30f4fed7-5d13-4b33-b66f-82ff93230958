<div class="ml-inference-new-deployment-modal h-full w-full bg-[#F1F3F9]">
  <div class="overflow-y-auto">
    <!-- HEADER -->
    <div
      class="flex items-center justify-between p-2 border-gray-200 dark:border-gray-700">
      <h2 mat-dialog-title>New Deployment</h2>
      <button
        mat-icon-button
        type="button"
        (click)="closeModal()"
        class="text-gray-400 hover:text-gray-600">
        <mat-icon>close</mat-icon>
      </button>
    </div>
    <hr />

    <div mat-dialog-content>
      <form [formGroup]="this.newDeploymentFormInstance" class="p-2">
        <!-- NAME + COMPUTE SECTION  -->
        <div class="grid grid-cols-10 gap-4 w-full">
          <!-- Name Section -->
          <div class="pb-6 col-span-7">
            <div class="pb-2">
              <h3>Name</h3>
            </div>
            <mat-form-field class="w-full" appearance="fill">
              <input matInput type="string" formControlName="deployment_name" />
            </mat-form-field>
          </div>

          <!-- Computer Section -->
          <div class="pb-6 col-span-3">
            <div class="pb-2">
              <h3>Compute type</h3>
            </div>
            <div formGroupName="compute_config" class="flex flex-row">
              <!-- Computer Type  -->
              <div class="">
                <mat-form-field class="w-full">
                  <mat-select formControlName="compute_type">
                    @for (
                      computeTypeOpt of this.computeConfigs;
                      track computeTypeOpt
                    ) {
                      <mat-option [value]="computeTypeOpt.id">{{
                        computeTypeOpt.label
                      }}</mat-option>
                    }
                  </mat-select>
                </mat-form-field>
              </div>

              <!-- TODO : Computer Resource  DISABLE for now. Not support for the config
             <div class="flex-none w-[20%] min-w-[120px]">
              <mat-form-field class="w-full">
                <mat-select formControlName="resources">
                  @for (
                    computeResourcesOpt of this.optionsForComputeResources;
                    track computeResourcesOpt
                  ) {
                    <mat-option [value]="computeResourcesOpt">{{
                      computeResourcesOpt
                    }}</mat-option>
                  }
                </mat-select>
              </mat-form-field>
            </div> -->
            </div>
          </div>
        </div>

        <!--  Env Config -->
        <div class="pb-6">
          <div class="pb-2">
            <h3>Environment</h3>
          </div>
          <div formGroupName="env_config" class="flex flex-row gap-4">
            <!-- Env Type  -->
            <!-- <div class="flex w-[20%] min-w-[120px]">
              <mat-form-field class="w-full">
                <mat-select
                  formControlName="env_type"
                  placeholder="Choose Type">
                  @for (envType of this.environmentTypes; track envType) {
                    <mat-option [value]="envType.id">{{
                      envType.label
                    }}</mat-option>
                  }
                </mat-select>
              </mat-form-field>
            </div> -->

            <!-- Shutdown type -->
            <div>
              <mat-form-field appearance="fill">
                <mat-select
                  formControlName="shutdown_type"
                  [compareWith]="compareShutdownTypes">
                  @for (type of this.shutDownTypes; track type) {
                    <mat-option [value]="type">{{ type.label }}</mat-option>
                  }
                </mat-select>
              </mat-form-field>
            </div>
            <!-- time : Only show when shutdown type is auto -->
            @if (
              this.newDeploymentFormInstance.controls.env_config.controls
                .shutdown_type.value.id === 'auto'
            ) {
              <div>
                <mat-form-field appearance="fill">
                  <input
                    matInput
                    placeholder="Time (in minutes)"
                    type="number"
                    formControlName="time_out" />
                </mat-form-field>
              </div>
            }
          </div>
          @if (
            this.newDeploymentFormInstance.controls.env_config.controls
              .shutdown_type.value.id === 'auto'
          ) {
            <div class="py-2">
              {{
                this.newDeploymentFormInstance.controls.env_config.controls
                  .shutdown_type.value.label
              }}
              is selected as shutdown criteria. Environment will run till time
              inputted in minutes.
            </div>
          } @else if (
            this.newDeploymentFormInstance.controls.env_config.controls
              .shutdown_type.value.id === 'manual'
          ) {
            <div class="py-2">
              {{
                this.newDeploymentFormInstance.controls.env_config.controls
                  .shutdown_type.value.label
              }}
              is selected as shutdown criteria. Environment will run till forced
              stopped.
            </div>
          }
        </div>

        <!-- Notes -->
        <div class="pb-6">
          <!-- LABEL HEADER -->
          <div class="pb-2">
            <h3>Notes</h3>
          </div>
          <!-- INPUT FIELD -->
          <div class="w-full">
            <mat-form-field class="w-full h-24">
              <textarea
                class="w-full h-full resize-none"
                matInput
                placeholder="Leave a comment"
                formControlName="notes">
              </textarea>
            </mat-form-field>
          </div>
        </div>
      </form>
    </div>

    <div mat-dialog-actions class="flex justify-end gap-2 mt-4">
      <button
        mat-flat-button
        [disabled]="this.newDeploymentFormInstance.invalid"
        type="submit"
        (click)="onSubmit()">
        Deploy
      </button>
    </div>
  </div>
</div>
