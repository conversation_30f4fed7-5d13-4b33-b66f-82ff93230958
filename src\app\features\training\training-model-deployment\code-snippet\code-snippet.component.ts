import { Component, Input } from '@angular/core';

import { Highlight } from 'ngx-highlightjs';
import { HighlightLineNumbers } from 'ngx-highlightjs/line-numbers';

@Component({
  selector: 'app-code-snippet',
  imports: [Highlight, HighlightLineNumbers],
  templateUrl: './code-snippet.component.html',
  styleUrl: './code-snippet.component.css',
})
export class CodeSnippetComponent {
  @Input() code = `function greet(name: string): string {
  return 'Example Code;
}
`;
  @Input() language = 'javascript';
}
