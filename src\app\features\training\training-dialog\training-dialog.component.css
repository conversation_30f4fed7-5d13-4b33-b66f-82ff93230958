.dialog-header {
  padding: 30px 30px 0 30px;
}

.dialog-header h2[mat-dialog-title] {
  font-family: <PERSON><PERSON>;
  font-weight: 400;
  font-size: 22px;
  line-height: 28px;
  letter-spacing: 0px;
}

.training-name {
  font-family: <PERSON><PERSON>;
  color: #43474e;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  letter-spacing: 0.25px;
  vertical-align: middle;
}

.dialog-content label {
  font-family: <PERSON>o;
  font-weight: 500;
  font-size: 16px;
  line-height: 24px;
  letter-spacing: 0.15px;
  vertical-align: middle;
}

.dialog-content button[mat-button] {
  border: 1px solid #e5e5f4;
}

.dialog-content {
  padding: 30px;
  overflow-y: auto;
  flex: 1;
}

.dialog-divider {
  width: 100%;
  height: 1px;
  /* background-color: #e0e0e0; */
  margin: 0;
}

::ng-deep .mat-mdc-dialog-container {
  width: 860px !important;
  height: 630px !important;
  max-width: 860px !important;
  max-height: 630px !important;
  position: fixed !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
  margin: 0 !important;
}

:host ::ng-deep .mat-horizontal-content-container {
  padding: 0px !important;
  height: 100% !important;
}

:host ::ng-deep .mat-mdc-dialog-content {
  padding: 0px !important;
}

:host ::ng-deep .mat-mdc-select-panel {
  margin-left: -10px;
}

:host ::ng-deep .mat-horizontal-stepper-wrapper {
  height: 100% !important;
}

:host ::ng-deep .mat-horizontal-stepper-content-current,
.mat-horizontal-stepper-content {
  height: 100% !important;
}

:host ::ng-deep .dialog-content .mat-mdc-select-arrow-wrapper::after {
  content: 'keyboard_arrow_down';
  font-family: 'Material Icons';
  font-size: 20px;
  color: #666;
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
}

.range {
  width: 120px !important;
}

.flex-grow {
  flex-grow: 1;
}

.dialog-actions {
  position: absolute;
  bottom: 25px;
}

::ng-deep .mat-horizontal-stepper-header {
  display: none !important;
}

::ng-deep .mat-stepper-horizontal-line {
  display: none;
}
