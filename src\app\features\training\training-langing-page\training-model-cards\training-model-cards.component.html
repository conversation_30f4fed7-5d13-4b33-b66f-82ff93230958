<div
  class="card-wrapper h-[68px] flex items-center"
  [class]="'status-button ' + projectInfo.status">
  <div class="flex items-center flex-1">
    <div class="menu ml-2">
      <button mat-icon-button [matMenuTriggerFor]="menu">
        <mat-icon>more_vert</mat-icon>
      </button>
      <mat-menu #menu="matMenu">
        <button mat-menu-item (click)="tryAnotherModel(projectInfo.id)">
          <mat-icon class="fill-none">fast_forward</mat-icon>
          <span>Try another model</span>
        </button>
        <button mat-menu-item (click)="stopTrainingModel()">
          <mat-icon>remove_circle_outline</mat-icon>
          <span>Stop Model</span>
        </button>
      </mat-menu>
    </div>
    <div class="title ml-2">
      <div class="heading font-medium text-lg">{{ projectInfo.name }}</div>
      <!-- <div class="status text-sm">{{ projectInfo.file.file_name }}</div> -->
    </div>
  </div>

  <div class="right-section">
    <!-- Vertical divider -->
    <div class="divider-line"></div>

    <div
      class="flex items-center"
      tabindex="0"
      (keydown)="
        handleStatusKeydown($event, projectInfo.status, projectInfo.id)
      ">
      <button
        [class]="'status-button ' + projectInfo.status"
        (click)="onStatusClick(projectInfo.status, projectInfo.id)">
        {{ getTrainingStatus(projectInfo.status).title }}
      </button>
      <button
        mat-icon-button
        class="status-icon-button"
        aria-label="Status action button">
        <mat-icon (click)="onStatusClick(projectInfo.status, projectInfo.id)">{{
          getTrainingStatus(projectInfo.status).icon
        }}</mat-icon>
      </button>
    </div>
  </div>
</div>
