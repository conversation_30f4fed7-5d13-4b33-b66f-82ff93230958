import { Component, inject } from '@angular/core';
import { TrainingAppStore } from '../store/training-app.store';
import { MatButtonModule } from '@angular/material/button';
import { TabContainerComponent } from './tab-container/tab-container.component';
import { MatDialog } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { NewDeploymentModalComponent } from './new-deployment-modal/new-deployment-modal.component';
import { InfoModalComponent } from './info-modal/info-modal.component';
@Component({
  selector: 'app-training-model-deployment',
  imports: [MatButtonModule, MatIconModule, TabContainerComponent],
  templateUrl: './training-model-deployment.component.html',
  styleUrl: './training-model-deployment.component.css',
})
export class TrainingModelDeploymentComponent {
  trainingAppStore = inject(TrainingAppStore);

  constructor(private dialog: MatDialog) {}

  openNewDeploymentModal() {
    this.dialog.open(NewDeploymentModalComponent, {
      minWidth: '70vw',
      minHeight: '30vh',
      enterAnimationDuration: '100ms',
      exitAnimationDuration: '100ms',
    });
  }

  openInfoModal() {
    this.dialog.open(InfoModalComponent, {
      minWidth: '70vw',
      minHeight: '30vh',
      enterAnimationDuration: '100ms',
      exitAnimationDuration: '100ms',
    });
  }
}
