// START : API SECTION : {{base_url}}/dlmodels/api/v1/dl-inference/{{dl_training_id}}/?cursor=0&limit=10

export type GetDlInferenceJobApiResponseData = DlInferenceInstance[];

export interface DlInferenceInstance {
  id: number;
  dl_training: number;
  status: {
    display_value: string;
    value: string;
  };
  ecs_task_id: string;
  error_msg: string | null;
  created_at: string; // ISO timestamp
  updated_at: string; // ISO timestamp
  configurations: Configurations;
}

export interface Configurations {
  feature_columns_all: FeatureColumn[];
  target_columns_categorical: TargetColumn;
}

export interface FeatureColumn {
  id: number;
  name: string;
  type: string;
  options: string[];
}

export interface TargetColumn {
  id: number;
  name: string;
  type: string;
  options: string[];
}
// END : API SECTION
