import { Component, inject, Input } from '@angular/core';
import { DlInferenceInstance } from '../../models/api-inference-deployment.model';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatDividerModule } from '@angular/material/divider';
import { MatMenuModule } from '@angular/material/menu';
import { TrainingAppStore } from '../../store/training-app.store';
import { NgClass } from '@angular/common';
import { MatDialog } from '@angular/material/dialog';
import { DeployedInferenceSnippetsModalComponent } from '../deployed-inference-snippets-modal/deployed-inference-snippets-modal.component';

@Component({
  selector: 'app-inference-instance-card',
  imports: [
    MatDividerModule,
    MatButtonModule,
    MatIconModule,
    MatMenuModule,
    NgClass,
  ],
  templateUrl: './inference-instance-card.component.html',
  styleUrl: './inference-instance-card.component.css',
})
export class InferenceInstanceCardComponent {
  @Input() instanceInfo!: DlInferenceInstance;
  trainingAppStore = inject(TrainingAppStore);

  constructor(private dialog: MatDialog) {}

  stopDlInference() {
    this.trainingAppStore.stopDlInferenceInstanceDeploymentProcess(
      String(this.instanceInfo.id),
    );
  }

  openDeployedInferenceSnippetModel() {
    this.dialog.open(DeployedInferenceSnippetsModalComponent, {
      minWidth: '70vw',
      minHeight: '30vh',
      enterAnimationDuration: '100ms',
      exitAnimationDuration: '100ms',
      data: {
        instanceInfo: this.instanceInfo,
      },
    });
  }
}
