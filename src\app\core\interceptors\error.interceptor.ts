/* eslint-disable @typescript-eslint/no-explicit-any */
import { Injectable } from '@angular/core';
import {
  Http<PERSON><PERSON>,
  <PERSON>ttp<PERSON><PERSON><PERSON>,
  HttpInterceptor,
  HttpRequest,
  HttpErrorResponse,
} from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { ToastrService } from 'ngx-toastr';
import { Router } from '@angular/router';
import { ErrorService } from '../services/error.service';

/**
 * Standard API Error Response Interface
 */
interface StandardErrorResponse {
  status: string;
  message: string | null;
  data: any;
  errors: {
    error: string;
  };
  pagination: any;
}

@Injectable()
export class ErrorInterceptor implements HttpInterceptor {
  constructor(
    private toastr: ToastrService,
    private router: Router,
    private errorService: ErrorService,
  ) {}

  intercept(
    req: HttpRequest<any>,
    next: <PERSON>ttp<PERSON><PERSON><PERSON>,
  ): Observable<HttpEvent<any>> {
    return next.handle(req).pipe(
      catchError((error: HttpErrorResponse) => {
        const errorMessage = this.extractStandardErrorMessage(error);

        if (errorMessage) {
          this.toastr.error(errorMessage);
        } else {
          // Get error message for user display
          this.toastr.error(this.errorService.getErrorMessage(error.status));
        }

        // Handle 401 Unauthorized errors
        if (error.status === 401) {
          console.warn('401 Unauthorized - Redirecting to login...');
          localStorage.clear();

          this.router
            .navigateByUrl('/auth', { skipLocationChange: true })
            .then(() => {
              this.router.navigate(['/auth/login']);
            });
        }

        return throwError(() => error);
      }),
    );
  }

  private extractStandardErrorMessage(error: HttpErrorResponse): string | null {
    const errorBody = error.error as StandardErrorResponse;

    // TODO::: Remove this check once backend is fixed
    if (
      errorBody?.errors?.error &&
      typeof errorBody.errors.error === 'string'
    ) {
      return errorBody.errors.error;
    }

    // TODO::: Remove this return `null` once backend is fixed
    // Return null for any non-standard format
    return null;
  }
}

// To test go to console under inspect and paste localStorage.setItem('access_token', 'eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyLCJleHAiOjE1MTYyNDAwMDB9.JxOas1u_1JbF1_01ru7gDPO3P_3wBa_FJcRQp5rpmc0');
// This throws unauthorized error
