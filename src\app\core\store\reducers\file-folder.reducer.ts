import { createReducer, on } from '@ngrx/store';
import { FileFolderState } from '../states/file-folder.state';
import {
  getFileFolderList,
  getFileFolderSuccess,
  resetFileState,
  setloading,
} from '../actions/file-folder.action';

const initialState: FileFolderState = {
  folder: {
    hierarchy: [],
    isLoading: false,
    loadedPaths: new Set<string>(),
  },
};

export const fileFolderReducer = createReducer(
  initialState,
  on(getFileFolderList, state => {
    return {
      ...state,
      folder: {
        ...state.folder,
        isLoading: true,
      },
    };
  }),
  on(getFileFolderSuccess, (state, { hierarchy, path }) => {
    return {
      ...state,
      folder: {
        loadedPaths: new Set(state.folder.loadedPaths).add(path),
        hierarchy: hierarchy,
        isLoading: false,
      },
    };
  }),
  on(setloading, (state, { isLoading }) => {
    return {
      ...state,
      folder: {
        // loadedPaths :new Set(state.folder.loadedPaths).add(path),
        // hierarchy: hierarchy,
        ...state.folder,
        isLoading: isLoading,
      },
    };
  }),
  on(resetFileState, () => {
    return {
      ...initialState,
    };
  }),
  // on(setLoadedPaths, (state, { path }) => {
  //   debugger
  //   return {
  //     ...state,
  //     folder: {
  //       ...state.folder,
  //       isLoading: false,
  //       loadedPaths :new Set(state.folder.loadedPaths).add(path)
  //     },
  //   };
  // })
);
