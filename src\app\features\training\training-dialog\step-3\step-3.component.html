<form
  [formGroup]="trainingForm"
  class="form-wrapper flex flex-col h-full space-y-6"
  novalidate>
  @if (isConfigEmpty()) {
    <app-empty-state-card
      class="h-full"
      [title]="'No Configuration Available'"></app-empty-state-card>
  } @else {
    @for (type of configTypes; track type) {
      <ng-container>
        @if (trainingForm && getConfigArray(type).length) {
          <div>
            <mat-expansion-panel
              [expanded]="true"
              class="bg-white shadow rounded-lg">
              <mat-expansion-panel-header>
                <mat-panel-title class="text-lg font-semibold capitalize">
                  {{ type.replace('_', ' ') }}
                </mat-panel-title>
              </mat-expansion-panel-header>

              <div
                [formArrayName]="type"
                class="grid grid-cols-1 md:grid-cols-2 gap-6 p-4">
                <ng-container
                  *ngFor="
                    let group of getConfigArray(type).controls;
                    let i = index
                  ">
                  <div [formGroupName]="i" class="space-y-2">
                    <mat-label class="block text-sm font-medium text-gray-700">
                      {{ group.get('name')?.value | titlecase }}
                      @if (group.get('required')?.value) {
                        <span class="text-red-500">*</span>
                      }
                    </mat-label>
                    <!-- If not feature_config -->
                    @if (type !== 'feature_config') {
                      <input
                        [type]="
                          ['int', 'float'].includes(
                            group.get('data_type')?.value
                          )
                            ? 'number'
                            : 'text'
                        "
                        formControlName="value"
                        [placeholder]="group.get('placeholder')?.value"
                        class="w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500" />
                      <!-- Error message -->
                    }
                    <!-- If feature_config -->
                    @else {
                      @if (group.get('data_type')?.value === 'list') {
                        <mat-select
                          multiple
                          class="w-full border border-gray-300 rounded focus:outline-none"
                          (selectionChange)="onFeatureSelection($event)"
                          formControlName="value">
                          <mat-option
                            *ngFor="let option of featureAndTargets.features"
                            [value]="option.id">
                            <mat-checkbox [checked]="option.checked">
                              {{ option.name }}
                            </mat-checkbox>
                          </mat-option>
                        </mat-select>
                      } @else {
                        <mat-select
                          class="w-full border border-gray-300 rounded focus:outline-none"
                          (selectionChange)="onTargetSelection($event)"
                          formControlName="value">
                          <mat-option
                            *ngFor="let option of featureAndTargets.targets"
                            [value]="option.id">
                            {{ option.name }}
                          </mat-option>
                        </mat-select>
                      }
                    }
                  </div>
                </ng-container>
              </div>
            </mat-expansion-panel>
          </div>
        }
      </ng-container>
    }
  }
</form>
