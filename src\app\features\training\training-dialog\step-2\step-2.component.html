<form [formGroup]="trainingForm" class="h-full">
  <div class="flex flex-col">
    @for (asset of assets; track asset) {
      <mat-label for="datasetVersion"
        >{{ asset.display_name }}<span class="text-red-500">*</span></mat-label
      >
      <app-file-folder-selection
        class="mb-4 w-100"
        [selectionType]="getSelectionType(asset.key)"
        (selected)="
          getSelectedFolder($event, asset.key)
        "></app-file-folder-selection>
    }
    <div class="flex flex-col gap-2">
      <mat-label class="block mb-1 font-medium">
        Dataset Split <span class="text-red-500">*</span>
      </mat-label>

      <div class="flex flex-col lg:flex-row gap-3">
        <!-- Train -->
        <div class="flex items-center gap-2 w-full lg:w-1/3">
          <span class="text-gray-500">Train</span>
          <input
            type="number"
            formControlName="train_size"
            min="0"
            max="100"
            class="flex-1 border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:text-black" />
          <span class="text-gray-500">%</span>
        </div>

        <!-- Test -->
        <div class="flex items-center gap-2 w-full lg:w-1/3">
          <span class="text-gray-500">Test</span>
          <input
            type="number"
            formControlName="test_size"
            min="0"
            max="100"
            class="flex-1 border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:text-black" />
          <span class="text-gray-500">%</span>
        </div>

        <!-- Validation -->
        <!-- <div class="flex items-center gap-2 w-full lg:w-1/3">
            <span class="text-gray-500">Validation</span>
            <input
              type="number"
              formControlName="validation_size"
              min="0"
              max="100"
              class="flex-1 border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500" />
            <span class="text-gray-500">%</span>
          </div> -->
      </div>
    </div>

    @if (
      trainingForm.get('train_size')?.errors ||
      trainingForm.get('test_size')?.errors
    ) {
      <div class="text-red-500 text-sm mt-1">
        Must be in range between 5 and 95
      </div>
    }
  </div>
</form>
