import { Component, inject } from '@angular/core';
import { MatDialogModule, MatDialogRef } from '@angular/material/dialog';

import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { FormBuilder, ReactiveFormsModule } from '@angular/forms';
import {
  NewDeploymentForm,
  shutdown_type_selection,
} from '../../service/new-deployment-form.service';
import { NewDeploymentFormService } from '../../service/new-deployment-form.service';

import { MatSelectModule } from '@angular/material/select';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatInputModule } from '@angular/material/input';
import { TrainingAppStore } from '../../store/training-app.store';
import { DlInferenceCodeSnippetBodyPayload } from '../../models/api-inference-code-snippet.model';

@Component({
  selector: 'app-new-deployment-modal',
  imports: [
    MatButtonModule,
    MatIconModule,
    ReactiveFormsModule,
    MatAutocompleteModule,
    MatIconModule,
    MatSelectModule,
    MatInputModule,
    MatFormFieldModule,
    ReactiveFormsModule,
    MatButtonModule,
    MatDialogModule,
  ],
  templateUrl: './new-deployment-modal.component.html',
  styleUrl: './new-deployment-modal.component.css',
})
export class NewDeploymentModalComponent {
  newDeploymentFormInstance!: NewDeploymentForm;
  trainingStore;

  //Compute Options:
  computeConfigs = [
    {
      id: 'CPU',
      label: 'CPU',
      resource_options: [
        'High-Memory – 64 GB RAM, Better for data-heavy workloads',
      ],
    },
    {
      id: 'GPU',
      label: 'GPU',
      resource_options: [
        'A100 Equivalent – 64 GB vRAM, 32 GB RAM, For large models',
      ],
    },
  ];

  optionsForComputeResources: string[] = [];

  // Environment Options
  shutDownTypes = [
    {
      label: 'Auto',
      id: 'auto',
    },
    {
      label: 'Manual',
      id: 'manual',
    },
  ];

  environmentTypes = [
    {
      label: 'On-Demand',
      id: 'On-Demand',
    },
    {
      label: 'Development',
      id: 'development',
    },
    {
      label: 'Production',
      id: 'production',
    },
  ];

  constructor(
    private popupRef: MatDialogRef<NewDeploymentModalComponent>,
    private fb: FormBuilder,
    private newDeploymentFormService: NewDeploymentFormService,
  ) {
    this.newDeploymentFormInstance = newDeploymentFormService.getInstance(null);
    this.trainingStore = inject(TrainingAppStore);
    /* TODO : DISABLE for now. Not support for the config

    //Resource is Dependent on what type is selected
    this.newDeploymentFormInstance.controls['compute_config'].controls[
      'compute_type'
    ].valueChanges.subscribe(change => {
      this.optionsForComputeResources =
        this.computeConfigs.find(config => config.id == change)
          ?.resource_options ?? [];
    });
    */
  }
  closeModal() {
    this.popupRef.close();
  }

  async onSubmit() {
    try {
      const formValues = this.newDeploymentFormInstance.getRawValue();
      const payload = {
        name: formValues.deployment_name,
        compute_type: formValues.compute_config.compute_type,
        shutdown_type: formValues.env_config.shutdown_type.id,
        timeout: formValues.env_config.time_out,
        notes: formValues.notes,
      } as DlInferenceCodeSnippetBodyPayload;

      const trainingId = this.trainingStore.trainingInfo().id;
      if (!trainingId) {
        throw Error('Unable to fetch trainingID from URL');
      }

      await this.trainingStore.startDlInferenceInstanceDeploymentProcess(
        trainingId,
        payload,
      );
      this.closeModal();
    } catch (err) {
      console.error('Error in onSubmit:', err);
    }
  }

  // HELPER METHODS:
  compareShutdownTypes = (
    a: shutdown_type_selection,
    b: shutdown_type_selection,
  ): boolean => {
    return a?.id === b?.id;
  };
}
