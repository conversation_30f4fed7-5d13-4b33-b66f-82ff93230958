<app-loader [loading]="isLoading()"></app-loader>

<!-- PAGE -->

<div class="flex flex-col h-full p-1">
  <!-- Header -->
  <div class="flex flex-row justify-between w-full h-26 px-6 pb-6">
    <div class="flex flex-col">
      <span class="text-base">Projects Name / Training</span>
      <span class="text-4xl">{{ projectName }}</span>
    </div>
  </div>

  <!-- Main Content -->
  <div class="flex-1 overflow-auto">
    <mat-drawer-container class="example-container h-full w-full">
      <mat-drawer mode="side" opened class="pr-6"
        ><mat-nav-list>
          <mat-list-item
            (click)="OnMenuItemSelect('overview')"
            [ngClass]="{ 'active-training-item': currentTab === 'overview' }"
            >Model Overview</mat-list-item
          >
          <mat-list-item
            (click)="OnMenuItemSelect('inference')"
            [ngClass]="{ 'active-training-item': currentTab === 'inference' }"
            *ngIf="
              subcategory() === 'Classification' ||
              subcategory() === 'Regression'
            "
            >Inference</mat-list-item
          >

          <mat-list-item
            (click)="OnMenuItemSelect('ai-model-deployment')"
            [ngClass]="{
              'active-training-item': currentTab === 'ai-model-deployment',
            }"
            *ngIf="
              subcategory() === 'Classification' ||
              subcategory() === 'Regression'
            ">
            AI Model Deployment</mat-list-item
          >

          <mat-list-item
            (click)="OnMenuItemSelect('metrics')"
            [ngClass]="{ 'active-training-item': currentTab === 'metrics' }"
            >Metrics</mat-list-item
          >
          <mat-list-item
            (click)="OnMenuItemSelect('report')"
            [ngClass]="{ 'active-training-item': currentTab === 'report' }"
            *ngIf="subcategory() === 'Classification'"
            >Classification Report</mat-list-item
          >
          <mat-list-item
            (click)="OnMenuItemSelect('confusion')"
            [ngClass]="{ 'active-training-item': currentTab === 'confusion' }"
            *ngIf="subcategory() === 'Classification'">
            Confusion Matrix</mat-list-item
          >
        </mat-nav-list></mat-drawer
      >
      <mat-drawer-content class="h-full">
        <div
          *ngIf="
            showTable &&
            (currentTab === 'overview' ||
              currentTab === 'metrics' ||
              currentTab === 'report')
          ">
          <app-training-table
            [trainingId]="trainingId"
            [currentTab]="currentTab"
            [tableData]="tableData"></app-training-table>
        </div>
        <div *ngIf="currentTab === 'confusion'">
          <plotly-plot [data]="plotData" [layout]="plotLayout"> </plotly-plot>
        </div>
        <div>
          <app-training-inference
            [trainingId]="trainingId"
            *ngIf="currentTab === 'inference'"></app-training-inference>
        </div>
        <div *ngIf="currentTab === 'ai-model-deployment'" class="h-full w-full">
          <app-training-model-deployment></app-training-model-deployment>
        </div>
        <div
          class="error-message"
          *ngIf="errorMsg"
          [innerHTML]="errorMsg"></div>
      </mat-drawer-content>
    </mat-drawer-container>
  </div>
</div>
