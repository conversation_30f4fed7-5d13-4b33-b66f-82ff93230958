import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  EventEmitter,
  inject,
  Input,
  OnDestroy,
  OnInit,
  Output,
  signal,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatMenuModule } from '@angular/material/menu';
import { MatIconModule } from '@angular/material/icon';
import { ActivatedRoute, Router } from '@angular/router';
import { TrainingService } from '../../service/training-service.service';
import { ToastrService } from 'ngx-toastr';
import { MatDialog } from '@angular/material/dialog';
import { TrainingDialogComponent } from '../../training-dialog/training-dialog.component';
import {
  MLTrainingProjectInfo,
  StartMlTrainingResponse,
  TrainingStatusInterface,
} from '../../models/training.model';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-training-model-cards',
  imports: [MatButtonModule, MatMenuModule, MatIconModule, CommonModule],
  templateUrl: './training-model-cards.component.html',
  styleUrl: './training-model-cards.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TrainingModelCardsComponent implements OnInit, OnDestroy {
  projectId: string | null = null;
  readonly dialog = inject(MatDialog);
  subscriptions: Subscription = new Subscription();

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private trainingService: TrainingService,
    private toasterService: ToastrService,
    private cdr: ChangeDetectorRef,
  ) {}

  readonly panelOpenState = signal(false);
  trainingStatus: TrainingStatusInterface = {
    completed: {
      title: 'Show Results',
      icon: 'keyboard_arrow_right',
    },
    configured: {
      title: 'Start Training',
      icon: 'play_arrow',
    },
    training: {
      title: 'Training',
      icon: 'hourglass_empty',
    },
    failed: {
      title: 'Failed',
      icon: 'rotate_left',
    },
    stopped: {
      title: 'Stopped',
      icon: 'rotate_left',
    },
  };
  @Input()
  projectInfo!: MLTrainingProjectInfo;
  @Output() updateTraining = new EventEmitter<boolean>();

  ngOnInit() {
    this.projectId = this.route.snapshot.paramMap.get('id');
  }
  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }

  // Performs a specific api call after clicking on the training status (Start Training, Failed, Show results)
  onStatusClick(status: string, id: number) {
    switch (status) {
      case 'completed':
      case 'failed': {
        this.router.navigate([
          `/project/${this.projectId}/training-results/${id}`,
        ]);
        break;
      }
      case 'stopped':
      case 'configured': {
        this.startMlTrainModel(id);
        break;
      }
    }
  }

  // to start the ML model training in BE
  startMlTrainModel(id: number) {
    this.projectInfo = { ...this.projectInfo, status: 'training' };
    this.cdr.markForCheck();
    this.subscriptions.add(
      this.trainingService.startMlTraining(id).subscribe({
        next: (res: StartMlTrainingResponse) => {
          const { message } = res;
          this.toasterService.success(message);
        },
        error: error => {
          this.projectInfo = { ...this.projectInfo, status: 'failed' };
          this.toasterService.error(error);
        },
      }),
    );
  }

  tryAnotherModel(id: number): void {
    const dialogRef = this.dialog.open(TrainingDialogComponent, {
      data: { ...this.projectInfo, id }, // Pass the `id` to the dialog if needed
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        console.log(
          `Dialog closed with result: ${result}, for model ID: ${id}`,
        );
        this.updateTraining.emit(true); // Emit the event only if result is meaningful
      } else {
        console.log(`Dialog closed without result for model ID: ${id}`);
      }
    });
  }

  getTrainingStatus(status: string) {
    return this.trainingStatus[status as keyof TrainingStatusInterface];
  }

  handleStatusKeydown(event: KeyboardEvent, status: string, id: number): void {
    if (event.key === 'Enter' || event.key === ' ') {
      this.onStatusClick(status, id);
      event.preventDefault(); // Prevent default scrolling for Space key
    }
  }

  stopTrainingModel() {
    console.log(this.projectInfo.id);
    this.trainingService.stopTrainModel(this.projectInfo.id).subscribe({
      next: response => {
        this.toasterService.success(response.message);
        this.projectInfo = { ...this.projectInfo, status: 'stopped' };
        this.cdr.detectChanges();
      },
    });
  }
}
