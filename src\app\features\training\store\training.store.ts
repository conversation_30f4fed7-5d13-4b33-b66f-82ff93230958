/* eslint-disable @typescript-eslint/no-explicit-any */

import { withDevtools } from '@angular-architects/ngrx-toolkit';
import { inject } from '@angular/core';
import { patchState, signalStore, withMethods, withState } from '@ngrx/signals';
import { TrainingService } from '../service/training-service.service';
import {
  DLConfiguration,
  DLModelInterface,
  TargetFeatureInterface,
} from '../models/training.model';

export const TrainingInitialState: TrainingState = {
  trainings: { list: [], isLoading: true },
  DLModels: [],
  category: [],
  subcategory: [],
  model: [],
  selectedModel: {
    category: '',
    subcategory: '',
    model: '',
    modelId: 0,
  },
  training_config: [],
  generation_config: [],
  model_config: [],
  feature_config: [],
  feature_list: [],
  target_list: [],
  totalProjects: 0,
};
export interface TrainingState {
  trainings: { list: any[]; isLoading: boolean };
  DLModels: DLModelInterface[];
  category: string[];
  subcategory: string[];
  model: { name: string; id: number }[];
  selectedModel: {
    category: string;
    subcategory: string;
    model: string;
    modelId: number;
  };
  training_config: DLConfiguration[];
  generation_config: DLConfiguration[];
  model_config: DLConfiguration[];
  feature_config: DLConfiguration[];
  feature_list: { name: string; id: number; checked?: boolean }[];
  target_list: { name: string; id: number; checked?: boolean }[];
  totalProjects: number;
}

export const TrainingStore = signalStore(
  { providedIn: 'root' },
  withState<TrainingState>(TrainingInitialState),
  withDevtools('TrainingStore'),
  withMethods((store, trainingService = inject(TrainingService)) => ({
    getDlModelTrainings(project_id: number, pageSize?: number) {
      trainingService.getDLModelTrainings(project_id, pageSize).subscribe({
        next: response => {
          patchState(store, state => ({
            ...state,
            trainings: {
              list: response.data,
              isLoading: false,
            },
            totalProjects: response?.pagination?.total,
          }));
        },
      });
    },
    loadDlModels(filterKey?: string) {
      trainingService
        .getDLModels(
          store.selectedModel.category(),
          store.selectedModel.subcategory(),
          store.selectedModel.model(),
        )
        .subscribe({
          next: response => {
            patchState(store, state => ({
              ...state,
              DLModels: response.data,
            }));
            this.getCategoryList(filterKey);
          },
        });
    },
    getCategoryList(filterKey?: string) {
      patchState(store, state => ({
        ...state,
        category:
          filterKey !== 'category'
            ? Array.from(new Set(state.DLModels.map(model => model.category)))
            : state.category,

        subcategory:
          filterKey !== 'subcategory'
            ? Array.from(
                new Set(state.DLModels.map(model => model.subcategory)),
              )
            : state.subcategory,

        model:
          filterKey !== 'model'
            ? Array.from(
                new Set(
                  state.DLModels.map(model => ({
                    name: model.name,
                    id: model.id,
                  })),
                ),
              )
            : state.model,
      }));
    },
    setSelectedModel(filterKey: string, value: string) {
      patchState(store, state => ({
        ...state,
        selectedModel: {
          ...state.selectedModel,
          [filterKey]: value,
          modelId: state.model.find(item => item.name === value)?.id ?? 0,
        },
      }));
      this.loadDlModels(filterKey);
    },

    setTrainingConfigurations() {
      if (store.selectedModel.modelId() !== 0) {
        const configurations = store.DLModels()[0].configurations;
        const modelConfig: any = {
          training_config: [],
          generation_config: [],
          model_config: [],
          feature_config: [],
        };
        for (const config of configurations) {
          switch (config.config_type) {
            case 'training_config':
              modelConfig.training_config.push(config);
              break;
            case 'generation_config':
              modelConfig.generation_config.push(config);
              break;
            case 'model_config':
              modelConfig.model_config.push(config);
              break;
            case 'feature_config':
              modelConfig.feature_config.push(config);
              break;
          }
        }
        patchState(store, state => ({
          ...state,
          training_config: modelConfig.training_config,
          generation_config: modelConfig.generation_config,
          model_config: modelConfig.model_config,
          feature_config: modelConfig.feature_config,
        }));
      }
    },

    getFeaturesTargets(file_id: number) {
      trainingService
        .getFeaturesTarget(file_id, store.selectedModel().modelId)
        .subscribe({
          next: response => {
            patchState(store, state => {
              const featureSection = response.data.results.find(
                (section: any) => section.data_type === 'list',
              );
              const targetSection = response.data.results.find(
                (section: any) => section.data_type !== 'list',
              );

              return {
                ...state,
                feature_list:
                  featureSection?.feature_list.map(
                    (feature: TargetFeatureInterface) => ({
                      id: feature.id,
                      name: feature.name,
                    }),
                  ) || [],
                target_list:
                  targetSection?.feature_list.map(
                    (feature: TargetFeatureInterface) => ({
                      id: feature.id,
                      name: feature.name,
                    }),
                  ) || [],
              };
            });
          },
        });
    },
    setResetState() {
      patchState(store, state => ({
        ...TrainingInitialState,
        trainings: {
          ...state.trainings,
          isLoading: false,
        },
      }));
    },
    resetAll() {
      patchState(store, () => ({
        ...TrainingInitialState,
      }));
    },
  })),
);
