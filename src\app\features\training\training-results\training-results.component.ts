import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  <PERSON><PERSON><PERSON><PERSON>,
  OnInit,
  signal,
} from '@angular/core';
import { MatSidenavModule } from '@angular/material/sidenav';
import { MatListModule } from '@angular/material/list';
import { MatIconModule } from '@angular/material/icon';
import { TrainingTableComponent } from './training-table/training-table.component';
import { TrainingService } from '../service/training-service.service';
import { ActivatedRoute } from '@angular/router';
import { ClassificationReport, Metric, ModelOverviewData } from './data';
import { CommonModule } from '@angular/common';
import { PlotlyViaCDNModule } from 'angular-plotly.js';
import { PlotData } from 'plotly.js';
import { Subscription } from 'rxjs';
import { ToastrService } from 'ngx-toastr';
import { SharedModule } from '../../../shared/shared.module';
import { TargetFeatureInterface } from '../models/training.model';
import { TrainingInferenceComponent } from '../training-inference/training-inference.component';
import { TrainingModelDeploymentComponent } from '../training-model-deployment/training-model-deployment.component';

@Component({
  selector: 'app-training-results',
  imports: [
    CommonModule,
    MatSidenavModule,
    PlotlyViaCDNModule,
    MatListModule,
    MatIconModule,
    TrainingTableComponent,
    SharedModule,
    TrainingInferenceComponent,
    TrainingModelDeploymentComponent,
  ],
  templateUrl: './training-results.component.html',
  styleUrl: './training-results.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TrainingResultsComponent implements OnInit, OnDestroy {
  trainingId = '';
  currentTab = 'overview';
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  resultData: any = {};
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  tableData: any = {};
  plotData: PlotData[] = [];
  plotLayout?: Partial<Plotly.Layout>;
  subcategory = signal<string>('');
  subscriptions: Subscription = new Subscription();
  showTable = false;
  projectName = '';
  status = '';
  errorMsg = '';
  isLoading = signal<boolean>(true);

  constructor(
    private trainingService: TrainingService,
    private route: ActivatedRoute,
    private cd: ChangeDetectorRef,
    private toasterService: ToastrService,
  ) {}

  ngOnInit() {
    this.trainingId = this.route.snapshot.params['id'];
    this.getTrainingOverview(this.trainingId);
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }

  getTrainingOverview(id: string) {
    this.trainingService.getMlTrainingInfo(Number(id)).subscribe({
      next: response => {
        this.status = response.data.status;
        if (this.status === 'failed') {
          this.resultData = response.data;
          this.isLoading.set(false);
          this.projectName = this.resultData.machine_learning_model.name;
          this.OnMenuItemSelect('overview');
          this.errorMsg = response.data.error_msg.replace(/\n/g, '<br>');
        } else {
          this.getTrainingResult(id);
        }
        this.cd.markForCheck();
      },
    });
  }
  getTrainingResult(id: string) {
    this.subscriptions.add(
      this.trainingService.getTrainingsResults(Number(id)).subscribe({
        next: response => {
          this.isLoading.set(false);
          this.resultData = response.data;
          this.subcategory.set(response.data.model.subcategory);
          this.projectName = 'project';
          // this.resultData.ml_training.machine_learning_model.name;
          this.OnMenuItemSelect('overview');
          if (this.subcategory() === 'Classification') {
            this.plotData =
              this.resultData?.results?.plots?.confusion_matrix_plot?.data;
            this.plotLayout =
              this.resultData?.results?.plots?.confusion_matrix_plot?.layout;
          }
          this.cd.markForCheck();
        },
        error: error => {
          this.isLoading.set(false);
          this.toasterService.error(error);
          this.cd.markForCheck();
        },
      }),
    );
    this.cd.markForCheck();
  }

  OnMenuItemSelect(selectedTab: string) {
    this.currentTab = selectedTab;
    this.isLoading.set(false);
    this.resultData = {
      ...this.resultData,
      selected_features: this.resultData?.ml_training?.selected_features
        ? this.resultData.ml_training.selected_features.map(
            (feature: TargetFeatureInterface) => feature?.name,
          )
        : this.resultData?.selected_features
          ? this.resultData.selected_features.map(
              (feature: TargetFeatureInterface) => feature?.name,
            )
          : [],

      selected_target:
        this.resultData?.selected_target?.name ||
        this.resultData?.ml_training?.selected_target?.name ||
        null,
    };
    switch (this.currentTab) {
      case 'overview':
        this.tableData = ModelOverviewData(this.resultData);
        break;
      case 'inference':
        this.showTable = false;
        break;
      case 'metrics':
        this.tableData = Metric(this.resultData);
        break;
      case 'report':
        this.tableData =
          this.subcategory() === 'Classification'
            ? ClassificationReport(this.resultData)
            : [];
        break;
    }
    this.cd.markForCheck();
    this.showTable =
      this.tableData?.displayData?.length > 0 &&
      this.currentTab !== 'confusion';
  }
}
