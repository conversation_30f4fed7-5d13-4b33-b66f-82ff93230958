import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Output } from '@angular/core';
import { MatOption } from '@angular/material/core';
import { MatIcon } from '@angular/material/icon';
import { MatSelect, MatSelectTrigger } from '@angular/material/select';
import { SharedModule } from '../../../../shared/shared.module';
import { FormsModule } from '@angular/forms';
import { SortOptions, SortTitle } from '../models';
import { MatTooltipModule } from '@angular/material/tooltip';

@Component({
  selector: 'app-data-view-sort',
  imports: [
    MatOption,
    MatIcon,
    CommonModule,
    MatSelect,
    SharedModule,
    FormsModule,
    MatSelectTrigger,
    MatTooltipModule,
  ],
  templateUrl: './data-view-sort.component.html',
  styleUrl: './data-view-sort.component.css',
})
export class DataViewSortComponent {
  selectedSortOption = {
    title: '',
    value: 'initialSelect',
  }; // Default selected option
  sortDropdownOptions = SortOptions;
  type = '';
  @Output() sortOptionChanged = new EventEmitter<{
    value: string;
    type: string;
  }>();

  onSortOptionChange(selectedSortOption: string): void {
    if (selectedSortOption === '') {
      this.selectedSortOption.value = 'initialSelect'; // Reset the sort if "Remove sort" is selected
    } else {
      this.selectedSortOption = {
        title: SortTitle[selectedSortOption as keyof typeof SortTitle],
        value: selectedSortOption,
      };
    }
    this.type = 'desc';
    this.sortOptionChanged.emit({
      value: this.selectedSortOption.value,
      type: this.type,
    });
  }

  sortData(event: MouseEvent) {
    event.stopPropagation();
    this.type = this.type == 'desc' ? 'asc' : 'desc';
    this.sortOptionChanged.emit({
      value: this.selectedSortOption.value,
      type: this.type,
    });
  }
}
