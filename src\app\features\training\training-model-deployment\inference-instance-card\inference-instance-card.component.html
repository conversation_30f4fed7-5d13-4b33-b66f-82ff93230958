<div
  class="inference-instances-container h-[60px] rounded-lg bg-white border [border-color:#79747E1F]">
  <div class="h-full w-full grid grid-cols-[70%_30%]">
    <div class="flex flex-row">
      <div class="h-full flex justify-center items-center">
        <button mat-icon-button [matMenuTriggerFor]="menu" aria-label="Menu">
          <mat-icon color="primary">more_vert</mat-icon>
        </button>
        <mat-menu #menu="matMenu">
          <button
            mat-menu-item
            (click)="this.stopDlInference()"
            [disabled]="this.instanceInfo.status.value !== 'started'">
            <mat-icon>block</mat-icon> Stop
          </button>
        </mat-menu>
      </div>
      <div class="flex flex-col justify-center items-start">
        <div class="font-medium">
          {{ this.instanceInfo.id }}
        </div>
        <div class="font-light text-gray-600">
          created at {{ this.instanceInfo.created_at }}
        </div>
      </div>
    </div>
    <div class="flex flex-row">
      <mat-divider
        vertical
        class="border-s [border-color:#CAC4D0] h-full"></mat-divider>
      <div class="flex-1 grid grid-cols-[70%_30%]">
        <div
          class="font-medium text-lg flex flex-row justify-center items-center">
          <div class="status-box">
            <span class="status-text flex justify-center items-center gap-2">
              <mat-icon>
                @switch (this.instanceInfo.status.value) {
                  @case ('running') {
                    hourglass_empty
                  }
                  @case ('started') {
                    play_arrow
                  }
                  @case ('stopped') {
                    block
                  }
                  @case ('failed') {
                    error
                  }
                  @default {
                    question_mark
                  }
                }
              </mat-icon>
              <span>{{ instanceInfo.status.display_value }}</span>
            </span>

            <div
              class="breath-overlay"
              [ngClass]="{
                'breath-green': instanceInfo.status.value === 'started',
                'breath-red': instanceInfo.status.value === 'stopped',
                'breath-yellow':
                  instanceInfo.status.value !== 'started' &&
                  instanceInfo.status.value !== 'stopped',
              }"></div>
          </div>
        </div>

        <!-- button section -->
        <div class="flex flex-row justify-evenly items-center">
          <div class="flex flex-row justify-center items-center">
            <button
              mat-icon-button
              (click)="openDeployedInferenceSnippetModel()">
              <mat-icon>code</mat-icon>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
