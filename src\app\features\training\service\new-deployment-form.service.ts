import { Injectable } from '@angular/core';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  FormControl,
  FormGroup,
  Valida<PERSON>,
} from '@angular/forms';

/** ---- field groups ------------------------------------------------------- */

export interface ComputeConfigFormField {
  compute_type: FormControl<string>;
  resources: FormControl<string>; // This is not required for now, but having code snippet for future use
}

export interface EnvConfigFormField {
  env_type: FormControl<string>;
  time_out: FormControl<string>;
  shutdown_type: FormControl<shutdown_type_selection>;
}

export interface shutdown_type_selection {
  id: string;
  label: string;
}

/** ---- full form ---------------------------------------------------------- */

export type NewDeploymentForm = FormGroup<{
  deployment_name: FormControl<string>;
  compute_config: FormGroup<ComputeConfigFormField>;
  env_config: FormGroup<EnvConfigFormField>;
  notes: FormControl<string | null>;
}>;

/** ---- data passed in to build the form ----------------------------------- */

export interface FormInitData {
  deployment_name: string;
  compute_type: string;
  resources: string;
  env_type: string;
  shutdown_type: shutdown_type_selection;
  time_out: string;
  notes?: string | null; // optional
}

@Injectable({ providedIn: 'root' })
export class NewDeploymentFormService {
  constructor(private fb: FormBuilder) {}

  private generateComputeConfigFormGroup(
    computeType: string,
    resources: string,
  ): FormGroup<ComputeConfigFormField> {
    return this.fb.nonNullable.group({
      compute_type: this.fb.nonNullable.control(
        { value: computeType, disabled: true }, // disabled as per the requirement
        Validators.required,
      ),
      resources: this.fb.nonNullable.control(resources),
    });
  }

  private generateEnvConfigFormGroup(
    envType: string,
    time_out: string,
    shutdown_type: shutdown_type_selection,
  ): FormGroup<EnvConfigFormField> {
    const group = this.fb.nonNullable.group({
      env_type: this.fb.nonNullable.control(envType),
      time_out: this.fb.nonNullable.control(time_out),
      shutdown_type: this.fb.nonNullable.control(
        shutdown_type,
        Validators.required,
      ),
    });

    /*
      timeout is required only when shutdown type is auto
      if shutdown type is manual, then timeout is not required
    */
    this.updateTimeoutValidator(group, shutdown_type?.id);

    group.controls.shutdown_type.valueChanges.subscribe(value => {
      this.updateTimeoutValidator(group, value?.id);
    });

    return group;
  }

  /*
      timeout is required only when shutdown type is auto
      if shutdown type is manual, then timeout is not required
    */
  private updateTimeoutValidator(
    group: FormGroup<EnvConfigFormField>,
    shutdownTypeId: string,
  ) {
    const timeOutControl = group.controls.time_out;

    if (shutdownTypeId === 'auto') {
      timeOutControl.setValidators([Validators.required]);
    } else {
      timeOutControl.clearValidators();
    }

    timeOutControl.updateValueAndValidity();
  }

  /** Build the full form */
  generateForm(init: FormInitData): NewDeploymentForm {
    return this.fb.group({
      deployment_name: this.fb.control(
        init.deployment_name ?? '', // default value (never null)
        { nonNullable: true, validators: [Validators.required] },
      ),
      compute_config: this.generateComputeConfigFormGroup(
        init.compute_type,
        init.resources,
      ),
      env_config: this.generateEnvConfigFormGroup(
        init.env_type,
        init.time_out,
        init.shutdown_type,
      ),

      notes: new FormControl<string | null>(init.notes ?? null),
    });
  }

  getInstance(initData: FormInitData | null | undefined) {
    if (initData == null || initData == undefined) {
      initData = {
        deployment_name: '',
        shutdown_type: {
          label: 'Auto',
          id: 'auto',
        },
        compute_type: 'CPU',
        resources: '',
        env_type: '',
        time_out: '',
        notes: '',
      };
    }
    return this.generateForm(initData);
  }

  //Helper functions
}
