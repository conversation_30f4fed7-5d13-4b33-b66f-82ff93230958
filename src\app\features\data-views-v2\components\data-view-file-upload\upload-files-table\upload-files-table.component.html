<div class="max-h-[400px] overflow-y-auto rounded">
  <table class="w-full border-collapse dark:bg-[#323539] dark:text-white">
    <thead>
      <tr class="bg-gray-200 dark:bg-[#323539]">
        @for (heading of tableHeading; track heading) {
          <th class="p-2 text-left font-bold">{{ heading }}</th>
        }
      </tr>
    </thead>
    <tbody>
      @for (
        file of uploadedFilesList;
        track trackByIndex($index);
        let i = $index
      ) {
        <tr
          class="border-b hover:bg-gray-50 dark:hover:bg-[#3a3d42]"
          [attr.data-index]="i">
          <td class="p-2">
            <div class="flex items-center">
              <mat-icon
                class="mr-2"
                [fontSet]="
                  getFileIcons(file.file.file) !== 'image'
                    ? 'material-icons-outlined'
                    : ''
                "
                >{{ getFileIcons(file.file.file) }}</mat-icon
              >
              <span class="truncate max-w-[200px]">{{
                file.file.file.name
              }}</span>
            </div>
          </td>
          <td class="p-2">
            {{ formatFileSize(file.file.file.size) }}
          </td>
          <td class="p-2">
            <button
              mat-icon-button
              (click)="removeFile(i)"
              [disabled]="file.uploading"
              aria-label="Remove file">
              <mat-icon>delete_outline</mat-icon>
            </button>
          </td>
        </tr>
      }
    </tbody>
  </table>
</div>
