<form [formGroup]="trainingForm" class="px-6 py-4 h-full">
  <div class="flex flex-col space-y-6">
    <!-- Name Input -->
    <div>
      <mat-label class="block mb-2 font-medium dark:text-white">
        Name <span class="text-red-500">*</span>
      </mat-label>
      <input
        type="text"
        formControlName="name"
        class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:text-black" />
    </div>
    <div class="flex flex-col gap-2">
      <!-- Group label -->
      <mat-label class="font-medium text-gray-700 dark:text-white">
        Category<span class="text-red-500">*</span>
      </mat-label>

      <!-- Select fields row -->
      <div class="flex flex-col lg:flex-row gap-3">
        <!-- Category -->
        <div class="w-full lg:w-1/3">
          <mat-select
            formControlName="category"
            (selectionChange)="getModelsByFilter('category', $event)"
            placeholder="Select Category"
            class="custom-input dark:text-black">
            <mat-option [value]="''">None</mat-option>
            @for (task of categoryList().categories; track task) {
              <mat-option [value]="task">{{ task }}</mat-option>
            }
          </mat-select>
        </div>

        <!-- Subcategory -->
        <div class="w-full lg:w-1/3">
          <mat-select
            formControlName="subcategory"
            (selectionChange)="getModelsByFilter('subcategory', $event)"
            placeholder="Select Sub Category"
            class="custom-input dark:text-black">
            <mat-option [value]="''">None</mat-option>
            @for (model of categoryList().subcategories; track model) {
              <mat-option [value]="model">{{ model }}</mat-option>
            }
          </mat-select>
        </div>

        <!-- Model -->
        <div class="w-full lg:w-1/3">
          <mat-select
            formControlName="model"
            (selectionChange)="getModelsByFilter('model', $event)"
            placeholder="Select Model"
            class="custom-input dark:text-black">
            <mat-option [value]="''">None</mat-option>
            @for (model of categoryList().model; track model) {
              <mat-option [value]="model">{{ model }}</mat-option>
            }
          </mat-select>
        </div>
      </div>
    </div>
  </div>
</form>
