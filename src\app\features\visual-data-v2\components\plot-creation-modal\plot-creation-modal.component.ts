import { Component, computed, inject, OnInit, signal } from '@angular/core';
import { MatDialogModule } from '@angular/material/dialog';
import { MatDialogRef } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import {
  MatButtonToggleChange,
  MatButtonToggleModule,
} from '@angular/material/button-toggle';
import { MatSelectChange, MatSelectModule } from '@angular/material/select';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import {
  FormArray,
  FormControl,
  FormGroup,
  FormBuilder,
  AbstractControl,
  Validators,
} from '@angular/forms';
import {
  DataSourceInputComponent,
  SelectionInfo,
} from '../data-source-input/data-source-input.component';
import {
  PlotTypesResponse,
  SettingSet,
  SmartbucketingSet,
} from '../../../../_models/visual-data/plot.model';
import { PlotService } from '../../services/plot.service';
import { ReactiveFormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import {
  PlotCreationForm,
  SettingSetFormGroup,
  OptionSet,
  SelectedOptionValue,
  CreatePlotPayload,
  OptionSetPayload,
  SelectedSmartBucket,
  BucketingSetPayload,
  SettingsSetPayload,
  FormPlotTypeValue,
} from './plot-creation-models';
import { MatOptionSelectionChange } from '@angular/material/core';
import { ColumnChoices } from '../../../data-views/models/data-view.model';

import { visualDataStore } from '../../store/visual-data.store';
import { MatIconModule } from '@angular/material/icon';

@Component({
  standalone: true,
  selector: 'app-plot-creation-modal',
  imports: [
    MatDialogModule,
    MatButtonModule,
    MatSelectModule,
    MatFormFieldModule,
    DataSourceInputComponent,
    ReactiveFormsModule,
    CommonModule,
    MatButtonToggleModule,
    MatProgressSpinnerModule,
    MatIconModule,
  ],
  templateUrl: './plot-creation-modal.component.html',
  styleUrls: ['./plot-creation-modal.component.css'],
})
export class PlotCreationModalComponent implements OnInit {
  //INJECRTIONS
  popupRef = inject(MatDialogRef<PlotCreationModalComponent>);
  fb = inject(FormBuilder);
  plotService = inject(PlotService);
  visualDataStore = inject(visualDataStore);

  //FORM
  plotCreationForm!: PlotCreationForm;

  plotTypes = signal<PlotTypesResponse>({
    Explain: [],
    Visualise: [],
    Standard: [],
    CompleteList: [],
  });

  //TO-DO : directly use below plotTypesArray, remove plotTypes as it is transform
  readonly plotTypesArray = computed(() =>
    Object.entries(this.plotTypes()).map(([key, values]) => ({ key, values })),
  );

  //STATE VARIABELS DATA which binds to form
  loading = false;
  availableSettings = signal<SettingSet[]>([]);

  availableDropdownSettingsArray = computed(() =>
    this.availableSettings()
      .filter(setting => setting.settings_type === 'Dropdown_Settings')
      .map(setting => setting.setting_name),
  );

  availableColumnChoices!: ColumnChoices['data'];
  availableAggregationChoices: string[] = [];
  availableSmartBucketChoices: SmartbucketingSet[] = [];

  availableSwitchSettingsArray = computed(() =>
    // TO-DO : Hardcoded values : Need better response from API for setting types
    this.availableSettings()
      .filter(setting => setting.settings_type === 'Switch_buttons')
      .map(setting => {
        const areaLineChart = {
          setting_name: 'make_area_chart',
          toggleOptions: ['Area', 'Line'],
        };

        const barColumnChart = {
          setting_name: 'make_bar_chart',
          toggleOptions: ['Bar', 'Column'],
        };

        if (setting.setting_name === 'make_area_chart') {
          return areaLineChart;
        } else if (setting.setting_name === 'make_bar_chart') {
          return barColumnChart;
        } else {
          throw new Error('Unknown setting name');
        }
      }),
  );

  async ngOnInit() {
    //LOAD PLOT TYPES:
    this.loadPlotTypes();

    //FORM INITIALZATION
    this.plotCreationForm = new FormGroup({
      plotType: this.fb.control(
        {
          plot_name: '',
          icon: '',
          category: '',
          slug: '',
        },
        { nonNullable: true, validators: Validators.required },
      ),
      selectedFileInfo: this.fb.group({
        fileName: this.fb.control('', {
          nonNullable: true,
          validators: Validators.required,
        }),
        fileId: this.fb.control('', {
          nonNullable: true,
          validators: Validators.required,
        }),
      }),
      selectedOptions: this.fb.array<OptionSet>([]),
      selectedSettings: this.fb.array<SettingSetFormGroup>([]),
    }) as PlotCreationForm;
  }

  closePlotCreationModal() {
    this.popupRef.close();
  }

  private async loadPlotTypes(): Promise<void> {
    try {
      const plotTypes = await this.plotService.fetchAllPlotTypes();
      if (plotTypes) {
        this.plotTypes.set(plotTypes); // Update the signal with fetched data
      }
    } catch (error) {
      console.error('Error loading plot types:', error);
    } finally {
      console.log('finally block');
    }
  }

  handleFileSelectionChange(event: SelectionInfo) {
    this.setFormFileName(event.name ?? '');
    this.setFormFileID(event.id ?? '');

    //TO-DO : Suggestions for drop down menus
    if (event.id !== null && event.id !== undefined && event.type === 'file') {
      this.plotService.getColumnChoiceAsync(Number(event.id)).then(res => {
        if (res.status === 'success') {
          console.log('Column choices:', res.data.data);
          this.availableColumnChoices = res.data.data;
        } else {
          console.error('Error fetching column choices:', res.message);
        }
      });
    }
  }

  setFormFileName(newValue: string) {
    this.plotCreationForm.controls['selectedFileInfo'].controls[
      'fileName'
    ].setValue(newValue);
  }

  setFormFileID(newValue: string) {
    this.plotCreationForm.controls['selectedFileInfo'].controls[
      'fileId'
    ].setValue(newValue);
  }

  //++++++++++ OPERATION RELATED TO OPTION SET IN FORMS
  toTitleCase(str: string) {
    return str
      .toLowerCase()
      .split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  }

  getSelectedOptionSetArray() {
    const selectoptArray = this.plotCreationForm.get(
      'selectedOptions',
    ) as FormArray<OptionSet>;
    return selectoptArray;
  }

  getValuesArrayFromOptionSet(optionSet: AbstractControl): FormArray {
    const val = optionSet.get('values') as FormArray<SelectedOptionValue>;
    return val;
  }

  getColumnNameForOptionSetBasedDataType(datatypes: string[] | undefined) {
    if (!datatypes) {
      return [];
    }
    const availableColumnChoicesForOptionSet: string[] = [];

    /*
    TO-DO : 
    Datatypes property name sent from API : /visualization/Plot/Pie%20Chart/  EXAMPLE : "NUMERICAL"
    is different than API : dataview/column-choices/${file_id}  EXAMPLE : "Numerical".
    One is ALL CAPS and other is small caps, we can't directly use .toLower() to direct comparision, here we have to define the type and TS compilier complains
    hence required to write more Data transformation
    */
    type ColumnDataType =
      | 'Categories'
      | 'Numerical'
      | 'TimeSpan'
      | 'DateTime'
      | 'Date'
      | 'Boolean'
      | 'Text';

    datatypes.forEach((dt: string) => {
      let dtInTileCase = this.toTitleCase(dt);

      if (dtInTileCase === 'Categorical') {
        /* TO-DO : API response has Categorical but in the code we are using Categories
        Response from  dataview/column-choices/${file_id} : give "categories" as response 
        but https://beta-backend.aicuflow.com/visualization/Plot/Pie%20Chart/ give "CATEGORICAL" -> Extra Data transformation for 
        */
        dtInTileCase = 'Categories';
      }

      if (
        this.availableColumnChoices &&
        (dtInTileCase as ColumnDataType) in this.availableColumnChoices
      ) {
        const choices =
          this.availableColumnChoices[dtInTileCase as ColumnDataType];
        availableColumnChoicesForOptionSet.push(...choices);
      }
    });
    return availableColumnChoicesForOptionSet;
  }
  getColumnNameFromOptionSetBasedOnOptSetFormControl(
    optionSet: AbstractControl,
  ): string[] {
    const datatypeControl = optionSet.get('datatype') as FormControl<string[]>;
    const datatypes = datatypeControl?.value ?? [];
    const availableColumnChoicesForOptionSet =
      this.getColumnNameForOptionSetBasedDataType(datatypes);
    return availableColumnChoicesForOptionSet;
  }

  addOptionValueInOptionSet(optionSet: AbstractControl) {
    const valuesOfOptionSet = this.getValuesArrayFromOptionSet(optionSet);
    valuesOfOptionSet.push(this.createSelectedOptionValueGroup());
  }

  hasAggregationPresentInOptionSet(optionSet: AbstractControl) {
    const val = optionSet.get('aggregations') as FormControl<boolean>;
    return val.value;
  }

  removeOptionValueFromOptionSet(
    optionSet: AbstractControl,
    index: number,
  ): void {
    const valuesArray = this.getValuesArrayFromOptionSet(optionSet);
    if (valuesArray.length > 1) {
      valuesArray.removeAt(index);
    }
  }

  private createOptionSetGroup(): OptionSet {
    return new FormGroup({
      displayValue: new FormControl<string>('', { nonNullable: true }),
      optionName: new FormControl<string>('', { nonNullable: true }),
      optionDescription: new FormControl<string>('', { nonNullable: true }),
      required: new FormControl<boolean>(false, { nonNullable: true }),
      multiple: new FormControl<boolean>(false, { nonNullable: true }),
      canSmartBucketed: new FormControl<boolean>(false, { nonNullable: true }),
      selectedSmartBucket: new FormControl<SelectedSmartBucket | null>(null),
      aggregations: new FormControl<boolean>(false, { nonNullable: true }),
      values: new FormArray<SelectedOptionValue>([]),
      datatype: new FormControl<string[]>([], { nonNullable: true }),
    }) as OptionSet;
  }

  private createSelectedOptionValueGroup(): SelectedOptionValue {
    return new FormGroup({
      selectedColName: new FormControl<string>('', { nonNullable: true }),
      selectedArggregation: new FormControl<string>('', { nonNullable: true }),
    }) as SelectedOptionValue;
  }
  //---- END

  //---- START :  OPERATION RELATED TO SETTING IN FORMS
  getSelectedSettingsArray() {
    const selectSettingsArray = this.plotCreationForm.get(
      'selectedSettings',
    ) as FormArray<SettingSetFormGroup>;
    return selectSettingsArray;
  }

  onDropDownSettingSelectionChange(event: MatOptionSelectionChange) {
    console.log(event);

    if (event.source.selected) {
      const selectedSettingsFormArray = this.getSelectedSettingsArray();
      selectedSettingsFormArray.controls.forEach(setting => {
        if (setting.get('settingName')?.value === event.source.viewValue) {
          setting.get('selectedSettingValue')?.setValue(true);
        }
      });
    } else {
      const selectedSettingsFormArray = this.getSelectedSettingsArray();
      selectedSettingsFormArray.controls.forEach(setting => {
        if (setting.get('settingName')?.value === event.source.viewValue) {
          setting.get('selectedSettingValue')?.setValue(false);
        }
      });
    }
  }

  onSwitchSettingSelectionChange(
    event: MatButtonToggleChange,
    settingName: string,
  ) {
    const selectedSettingsFormArray = this.getSelectedSettingsArray();
    selectedSettingsFormArray.controls.forEach(setting => {
      // TO-DO : Hardcoded values for switch setting type : Need better response from API for setting types
      if (setting.get('settingName')?.value === settingName) {
        if (settingName === 'make_area_chart' && event.value === 'Area') {
          setting.get('selectedSettingValue')?.setValue(true);
        } else if (
          settingName === 'make_area_chart' &&
          event.value === 'Line'
        ) {
          setting.get('selectedSettingValue')?.setValue(false);
        } else if (settingName === 'make_bar_chart' && event.value === 'Bar') {
          setting.get('selectedSettingValue')?.setValue(true);
        } else if (
          settingName === 'make_bar_chart' &&
          event.value === 'Column'
        ) {
          setting.get('selectedSettingValue')?.setValue(false);
        } else {
          throw new Error('Unknown setting name');
        }
      }
    });
  }

  //---- END

  private createPlotPayload(): CreatePlotPayload {
    const payload: CreatePlotPayload = {
      plot_name: this.plotCreationForm.value.plotType?.plot_name ?? '',
      selected_options: [],
      selected_settings: [],
      selected_smart_bucketing: [],
    };

    this.plotCreationForm.value.selectedOptions?.forEach(
      selectedOptionFromForm => {
        console.log('selectedOption:', selectedOptionFromForm);
        const values = selectedOptionFromForm.values;
        if (values) {
          values.forEach((val, idx) => {
            const datatypes = selectedOptionFromForm.datatype;
            const columnChoices =
              this.getColumnNameForOptionSetBasedDataType(datatypes);

            const selectedOption: OptionSetPayload = {
              display_value: selectedOptionFromForm.displayValue ?? '',
              option_name: selectedOptionFromForm.optionName ?? '',
              selected_column_name: val.selectedColName ?? '',
              isMultiple: selectedOptionFromForm.multiple ?? false,
              isRequired: selectedOptionFromForm.required ?? false,
              columnOptions: columnChoices,
              // TO-DO : isChild ??? Keep Better API Payload
              isChild: idx === 0 ? false : true,
              selected_smart_bucketing:
                selectedOptionFromForm.selectedSmartBucket !== null
                  ? true
                  : false,
            };

            // Aggregation
            if (val.selectedArggregation) {
              selectedOption.selected_aggregation = val.selectedArggregation;
            }

            payload.selected_options.push(selectedOption);

            // If the option set has smart bucketing, add it to the payload
            if (selectedOptionFromForm.selectedSmartBucket) {
              payload.selected_smart_bucketing.push({
                smart_bucketing_name:
                  selectedOptionFromForm.selectedSmartBucket
                    .smart_bucketing_name,
                smart_bucketing_value:
                  selectedOptionFromForm.selectedSmartBucket
                    .smart_bucketing_value,
              } as BucketingSetPayload);
            }
          });
        }
      },
    );

    // Push the selected settings to the payload
    this.plotCreationForm.value.selectedSettings?.forEach(
      selectedSettingFromForm => {
        const settingSetPayload: SettingsSetPayload = {
          setting_name: selectedSettingFromForm.settingName ?? '',
          selected_setting_value:
            selectedSettingFromForm.selectedSettingValue ?? false,
        };
        payload.selected_settings.push(settingSetPayload);
      },
    );
    return payload;
  }

  async onSubmit() {
    console.log(this.plotCreationForm.value);

    if (this.plotCreationForm.invalid) {
      // TO-DO : use toaster to show error message
      console.error('Form is invalid');
      return;
    }
    const payload = this.createPlotPayload();

    try {
      this.loading = true;
      if (this.plotCreationForm.value.selectedFileInfo?.fileId !== null) {
        const fileId = Number(
          this.plotCreationForm.value.selectedFileInfo?.fileId,
        );
        await this.visualDataStore.addNewPlotToDb(fileId, payload);
        this.visualDataStore.loadUserPlotData({ cursor: 0 });
        this.closePlotCreationModal();
      }
      this.loading = false;
    } catch (error) {
      console.error('Error creating plot:', error);
    }
  }

  _getSettingSetGroupFromSettingSetApiRes(settingSetsAPI: SettingSet[]) {
    const settingFormArray = [] as SettingSetFormGroup[];
    settingSetsAPI.forEach(setting => {
      const settingGroup = new FormGroup({
        settingName: new FormControl<string>(setting.setting_name, {
          nonNullable: true,
        }),
        selectedSettingValue: new FormControl<boolean>(false, {
          nonNullable: true,
        }),
      }) as SettingSetFormGroup;

      settingFormArray.push(settingGroup);
    });
    return settingFormArray;
  }

  async onPlotTypeSelectionChange(event: MatSelectChange) {
    const selectedValue = event.value as FormPlotTypeValue;
    const res = await this.plotService.getPlotInfoAsync(selectedValue.slug);

    //option set
    const availableOptionSets = res.data.option_set;
    const optionsFormArray = this.getSelectedOptionSetArray();
    optionsFormArray.clear(); // Clear existing entries

    availableOptionSets.forEach(option => {
      //create group for each option set
      const optionGroup = this.createOptionSetGroup();

      optionGroup.patchValue({
        displayValue: option.display_value ?? '',
        optionName: option.option_name ?? '',
        optionDescription: option.option_description ?? '',
        required: option.required ?? false,
        multiple: option.multiple ?? false,
        aggregations: option.aggregations ?? false,
        canSmartBucketed: option.smart_bucketing ?? false,
        selectedSmartBucket: null,
        datatype: option.datatype ?? [],
      });

      const valuesArray = this.getValuesArrayFromOptionSet(optionGroup);
      valuesArray.clear();
      valuesArray.push(this.createSelectedOptionValueGroup());

      // push this optionGroup to the form
      optionsFormArray.push(optionGroup);

      // Setting set
      this.availableSettings.set(res.data.setting_set);
      /* TO-DO : if the setting of type dropdown, then why API response has
      seperate object for each  dropdown setting?
      this should be a single object with multiple values to convey its meaning.

      For example:
      {
        setting_name: 'setting1',
        setting_description: 'description1',
        multiple: false,
        settings_type: 'Dropdown_Settings',
        values: ['value1', 'value2']
      }

      In current API response, it is like:
      [
        {
          setting_name: 'setting1',
          setting_description: 'description1',
          multiple: false,
          settings_type: 'Dropdown_Settings',
          values: 'value1'
        },
        {
          setting_name: 'setting1',
          setting_description: 'description1',
          multiple: false,
          settings_type: 'Dropdown_Settings',
          values: 'value2'
        }
      ]
      
      We have to do data transformation to get the values of dropdown settings. instead better response from API is expected.

      */

      const selectedSettingsFormArray = this.getSelectedSettingsArray();
      selectedSettingsFormArray.clear(); // Clear existing entries
      const settingFormGroupList = this._getSettingSetGroupFromSettingSetApiRes(
        this.availableSettings(),
      );
      settingFormGroupList.forEach(fromGroup => {
        selectedSettingsFormArray.push(fromGroup);
      });
    });

    // Get Aggregation choices
    this.availableAggregationChoices = res.data.aggregation_set.map(aggSet => {
      return aggSet.aggregation_type;
    });

    // Get Smart Bucket choices
    this.availableSmartBucketChoices = res.data.smartbucketing_set;
  }
}
