/* eslint-disable @typescript-eslint/no-explicit-any */
import { Component, effect, inject, Input } from '@angular/core';
import { FileFolderSelectionComponent } from '../../../../shared/components/file-folder-selection/file-folder-selection.component';
import {
  Form<PERSON>rray,
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { TrainingStore } from '../../store/training.store';
import { MatFormFieldModule } from '@angular/material/form-field';

@Component({
  selector: 'app-step-2',
  imports: [
    FileFolderSelectionComponent,
    ReactiveFormsModule,
    MatFormFieldModule,
  ],
  templateUrl: './step-2.component.html',
  styleUrl: './step-2.component.css',
})
export class Step2Component {
  @Input() trainingForm!: FormGroup;
  assets: any[] = [];
  readonly store = inject(TrainingStore);

  constructor(private formBuilder: FormBuilder) {}
  getSelectionType(key: string) {
    const parts = key.split('_');
    return parts.length > 1 ? parts[1] : '';
  }

  effect = effect(() => {
    this.assets = this.store.DLModels()[0].assets;
    const assetsFormGroup = this.trainingForm.get('assets') as FormGroup;

    // Remove any controls not matching current assets (clean up first)
    Object.keys(assetsFormGroup.controls).forEach(key => {
      if (!this.assets.find(asset => asset.key === key)) {
        assetsFormGroup.removeControl(key);
      }
    });

    // Add controls for each asset key if missing
    this.assets.forEach(asset => {
      if (!assetsFormGroup.contains(asset.key)) {
        assetsFormGroup.addControl(
          asset.key,
          this.formBuilder.control([], Validators.required),
        );
      }
    });
  });

  getSelectedFolder(event: any, asset_key: string) {
    const selectedFileFolder = event.checked
      ? {
          key: asset_key,
          [event.type === 'folder' ? 'folder_path' : 'file_id']:
            event.id === 0 ? event.path.slice(1) : event.id,
        }
      : {};

    const assetsFormGroup = this.trainingForm.get('assets') as FormGroup;
    assetsFormGroup.get(asset_key)?.setValue(selectedFileFolder);
  }

  getConfigArray(type: string): FormArray {
    return this.trainingForm.get(type) as FormArray;
  }
}
