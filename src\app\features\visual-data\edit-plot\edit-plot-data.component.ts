/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/no-unused-vars */
import {
  ChangeDetectorRef,
  Component,
  computed,
  effect,
  EventEmitter,
  inject,
  input,
  model,
  OnDestroy,
  OnInit,
  Output,
  signal,
} from '@angular/core';
import { g_const } from '../../../_utility/global_const';
import { PlotService } from '../../../services/plot.services';
import { DataviewService } from '../../data-views/services/data-view.service';
import { MatSelectChange } from '@angular/material/select';
import { ToastrService } from 'ngx-toastr';

import {
  AbstractControl,
  FormArray,
  FormBuilder,
  FormControl,
  FormGroup,
  Validators,
} from '@angular/forms';
import { firstValueFrom, Subject, takeUntil } from 'rxjs';
import {
  CreatePlotPayload,
  OptionSet,
} from '../../../_models/visual-data/explore-view.model';
import {
  PlotResponse,
  ToggleButtonData,
  ToggleButtonDefinition,
  UserPlot,
} from '../../../_models/visual-data/visual-data.model';
import {
  File,
  Folder,
  PlotTypesResponse,
  SettingSet,
  SmartbucketingSet,
  Subfolder,
} from '../../../_models/visual-data/plot.model';
import { MatButtonToggleChange } from '@angular/material/button-toggle';

export interface ListItem {
  id: number | string;
  text: string | number;
  isDisabled?: boolean;
}

interface Group {
  option_name: string;
  selected_column_name: FormControl | string;
  isMultiple: boolean;
  isRequired: boolean;
  selected_aggregation?: FormControl | string;
  isSmartBucketing?: boolean;
  columnOptions?: FormArray | string[];
  isChild?: boolean;
}

@Component({
  selector: 'app-edit-plot-data',
  templateUrl: './edit-plot-data.component.html',
  styleUrls: ['./edit-plot-data.component.css'],
  // eslint-disable-next-line @angular-eslint/prefer-standalone
  standalone: false,
})
export class EditPlotDataComponent implements OnInit, OnDestroy {
  @Output() closeModal = new EventEmitter<boolean>();
  selectedPlotTypeName = model<string | null>(null);
  selectedPlotId = input.required<number>();
  plotService = inject(PlotService);
  selectedPlotType = computed(
    () =>
      this.plotTypes().CompleteList.find(
        pt => pt.plot_name === this.selectedPlotTypeName(),
      ) ?? null,
  );
  selectedPlot = signal<UserPlot | null>(null);

  effect = effect(() => {
    console.log('selectedname', this.selectedPlotType());
    this.getPlotTypeInfo();
  });

  g_const = g_const;
  loading = false;
  @Output() cancelEvent = new EventEmitter<boolean>();
  @Output() saveEvent = new EventEmitter<PlotResponse['response']>();

  // @Input() fetchPlotEditInfo?: (plotId: number) => Observable<any>;  // Input expects an Observable

  graphForm: FormGroup;
  plotTypes = signal<PlotTypesResponse>({
    Explain: [],
    Visualise: [],
    Standard: [],
    CompleteList: [],
  });
  selectedSmartBucketing: any;
  aggregationList: string[] | undefined;
  settingsList: string[] = [];
  smartBucketingList: SmartbucketingSet[] = [];
  dropdownSettingsMultiple = true;
  showValidationError = false;
  _unsubscribe = new Subject();
  isSmartBucketingEnabled = false;
  isSettingsEnabled = false;
  numericalList: string[] = [];
  categoriesList: string[] = [];
  DateList: string[] = [];
  DateTimeList: string[] = [];
  TimeSpanList: string[] = [];
  filterSmartBucket = false;
  filterSmartBucketDataTypes: string[] = [];
  selectedOptionsList: string[] = [];
  sortByColName: string[] = [];
  selectedFileID = 0;

  folders: Folder[] = [];
  allFiles: File[] = [];
  hasNextList: string | null = null;
  hasPreviousList: string | null = null;

  isFileListVisible = false;
  selectedFileName = '';
  searchControl = new FormControl('');

  formControlSettings = new FormControl<string[] | null>([]);
  formControlPlotVariant = new FormControl<ToggleButtonDefinition | null>(null);
  switchOptions = signal<ToggleButtonDefinition[]>([]);

  constructor(
    private exploreViewService: PlotService,
    private formBuilder: FormBuilder,
    private dataviewService: DataviewService,
    private toastrService: ToastrService,
    private cd: ChangeDetectorRef,
  ) {
    this.searchControl.valueChanges.subscribe(searchTerm => {
      if (searchTerm == '') {
        const lowerCaseSearchTerm = searchTerm.toLowerCase();
        this.hierarchicalFiles = this.hierarchicalFiles.map(folder => ({
          ...folder,
          files: folder.files.filter(file =>
            file.file_name.toLowerCase().includes(lowerCaseSearchTerm),
          ),
          subfolders: this.filterSubfolders(
            folder.subfolders,
            lowerCaseSearchTerm,
          ),
        }));
        this.cd.detectChanges();
      } else {
        this.filterFiles(searchTerm || '');
      }
    });
    this.graphForm = this.formBuilder.group({});
  }

  async ngOnInit() {
    this.loading = true;
    const userPlotInEdit = await this.fetchPlotEditInfo(this.selectedPlotId());
    if (userPlotInEdit !== null) {
      this.selectedPlot.set(userPlotInEdit);
      this.selectedFileID = userPlotInEdit.file_id;
      this.selectedFileName = userPlotInEdit.file_name;
      await this.setSelectedPlotType(userPlotInEdit.plot_name);
      this.loadFileList();
      await this.getAllPlotTypes();
    }

    // this.fetchPlotEditInfo_id(this.plotId)
  }

  async fetchPlotEditInfo(plotId: number): Promise<UserPlot | null> {
    try {
      return (await firstValueFrom(this.plotService.getPlotEditInfo(plotId)))
        .data;
    } catch (e) {
      console.error('Error fetching plot edit info:', e);
      return null;
    }
  }

  async getAllPlotTypes(): Promise<void> {
    try {
      const res = await firstValueFrom(this.exploreViewService.getAllPlots());
      if (res.status === 'success') {
        this.plotTypes.set(res.data); // res.data has the plot lists
        this.plotTypes.update(plotTypes => ({
          ...plotTypes,
          CompleteList: [...plotTypes.Visualise, ...plotTypes.Explain],
        }));
        if (this.plotTypes()) {
          this.loading = false;
        }
      } else {
        this.loading = false;
      }
    } catch (e: unknown) {
      console.error('Error while fetching plot types:' + e);
    }
  }

  setSelectedPlotTypeFromChangeEvent(event: MatSelectChange): void {
    this.setSelectedPlotType(event.value.plot_name);
  }

  async setSelectedPlotType(plotTypeName: string) {
    this.selectedOptionsList = [];
    this.showValidationError = false;
    this.selectedPlotTypeName.set(plotTypeName);
    if (this.selectedFileID > 0) {
      await this.getColumnsData(Number(this.selectedFileID));
      this.getPlotTypeInfo();
    }
  }

  // Function to load the next set of files
  loadNextFiles(): void {
    if (this.hasNextList) {
      this.fetchPageData(this.hasNextList);
    }
  }

  // Function to load the previous set of files
  loadPreviousFiles(): void {
    if (this.hasPreviousList) {
      this.fetchPageData(this.hasPreviousList);
    }
  }

  // Function to fetch files by navigating between pages
  fetchPageData(url: string): void {
    this.dataviewService.getNextPageByUrl(url).subscribe(
      response => {
        if (response.status === 'success') {
          this.hierarchicalFiles = Object.values(response.data);
          this.hasNextList = response.pagination?.next ?? null;
          this.hasPreviousList = response.pagination?.previous ?? null;
        } else {
          this.toastrService.error(
            'Error retrieving data: ' + response.message,
          );
        }
      },
      error => {
        console.error('Error fetching data:', error);
        // Error handling is done by ErrorInterceptor - no need for local toasts
      },
    );
  }

  toggleFileList() {
    this.isFileListVisible = !this.isFileListVisible;
    this.filterFiles(this.searchControl.value || '');
  }

  // Function to handle file selection
  async onFileSelect(file: any) {
    this.selectedFileName = file.file_name;
    this.isFileListVisible = false;

    if (file) {
      this.selectedFileID = file.file_id;
      await this.getColumnsData(Number(file.file_id));
    }
  }

  // Function to filter files by name
  filterFiles(searchTerm: string): void {
    const lowerCaseSearchTerm = searchTerm.toLowerCase();

    this.hierarchicalFiles = this.hierarchicalFiles.map(folder => ({
      ...folder,
      files: folder.files.filter(file =>
        file.file_name.toLowerCase().includes(lowerCaseSearchTerm),
      ),
      subfolders: this.filterSubfolders(folder.subfolders, lowerCaseSearchTerm),
    }));
  }

  // Function to recursively filter subfolders by name
  filterSubfolders(subfolders: any[], searchTerm: string): any[] {
    return subfolders
      .map(subfolder => ({
        ...subfolder,
        files: subfolder.files.filter((file: any) =>
          file.file_name.toLowerCase().includes(searchTerm),
        ),
        subfolders: this.filterSubfolders(subfolder.subfolders, searchTerm),
      }))
      .filter(
        subfolder =>
          subfolder.files.length > 0 || subfolder.subfolders.length > 0,
      );
  }

  // Function to reset the file list to its original state
  resetFileList(): void {
    this.loadFileList();
  }

  loadFileList(): void {
    const project_id = Number(localStorage.getItem('project_id'));
    this.exploreViewService.plotFileList(project_id).subscribe(
      response => {
        if (response.status === 'success') {
          this.folders = response.data;
          this.hasNextList = response.pagination?.next ?? null;
          this.hasPreviousList = response.pagination?.previous ?? null;
          this.extractFiles();
        }
      },
      error => {
        console.error('Error fetching plot data', error);
      },
    );
  }

  hierarchicalFiles: {
    folder_name: string;
    files: File[];
    subfolders: {
      folder_name: string;
      files: File[];
      subfolders: any[];
    }[];
  }[] = [];

  extractFiles() {
    this.folders.forEach(folder => {
      this.buildHierarchy(folder);
    });
  }

  uniqueBy<T>(array: T[], key: keyof T) {
    return [...new Map(array.map(item => [item[key], item])).values()];
  }

  private buildHierarchy(folder: Folder): any {
    const folderData = {
      folder_name: folder.folder_name,
      files: this.uniqueBy(folder.files, 'file_id'),
      subfolders: [] as {
        folder_name: string;
        files: File[];
        subfolders: any[];
      }[],
    };

    if (folder.subfolders && folder.subfolders.length > 0) {
      folder.subfolders.forEach(subfolder => {
        const subfolderData = this.buildHierarchy(subfolder);
        folderData.subfolders.push(subfolderData);
      });
    }
    const existingFolder = this.hierarchicalFiles.find(
      f => f.folder_name === folderData.folder_name,
    );
    if (!existingFolder) {
      this.hierarchicalFiles = [folderData];
    } else {
      existingFolder.files = this.uniqueBy(
        [...existingFolder.files, ...folderData.files],
        'file_id',
      );
      existingFolder.subfolders.push(...folderData.subfolders);
    }
    return folderData;
  }

  extractSubfolderFiles(subfolders: Subfolder[]) {
    subfolders.forEach((subfolder: Subfolder) => {
      this.allFiles.push(...subfolder.files);
      if (subfolder.subfolders && subfolder.subfolders.length > 0) {
        this.extractSubfolderFiles(subfolder.subfolders);
      }
    });
  }

  async getColumnsData(file_id: number): Promise<void> {
    try {
      const response = await firstValueFrom(
        this.dataviewService.getColumnChoice(file_id),
      );
      this.numericalList = response.data.data.Numerical;
      this.categoriesList = response.data.data.Categories;
      this.DateList = response.data.data.Date;
      this.DateTimeList = response.data.data.DateTime;
      this.TimeSpanList = response.data.data.TimeSpan;
    } catch (e) {
      console.error('Error columns data:', e);
    }
  }

  getPlotTypeInfo(): void {
    this.exploreViewService
      .getPlotInfo(this.selectedPlotType()?.plot_name ?? null)
      .subscribe(res => {
        this.isSmartBucketingEnabled = false;
        this.isSettingsEnabled = true;
        console.log('name', this.selectedPlotType()?.plot_name);
        const selectedPlotType = this.plotTypes().CompleteList.find(
          pt => pt.plot_name === this.selectedPlotType()?.plot_name,
        );
        this.graphForm = this.formBuilder.group({
          plot_name: selectedPlotType,
          selected_options: this.formBuilder.array([]),
          selected_settings: this.formBuilder.array([]),
          selected_smart_bucketing: this.formBuilder.array([]),
        });

        this.filterSmartBucket = false;

        res.data.option_set?.forEach((element: OptionSet) => {
          if (element.smart_bucketing) {
            this.filterSmartBucket = true;
            this.filterSmartBucketDataTypes = element.datatype;
          }

          const selectedOptionForElement =
            this.selectedPlot()?.selected_options.find(
              so => so.option_name === element.option_name,
            );
          this.setSelectedOptions(
            element.option_name,
            element.required,
            selectedOptionForElement
              ? selectedOptionForElement.selected_aggregation
              : false,
            element.multiple,
            selectedOptionForElement
              ? selectedOptionForElement.selected_column_name
              : '',
            element.smart_bucketing,
            element.datatype,
          );
        });
        this.settingsList = [];
        this.formControlSettings.setValue([]);
        res.data.setting_set?.forEach((setting: SettingSet) => {
          const selectedSettingForElement =
            this.selectedPlot()?.selected_settings.find(
              ss => ss.setting_name === setting.setting_name,
            );
          console.log(this.graphForm.value.plot_name.plot_name);
          if (setting.settings_type == 'Dropdown_Settings') {
            this.settingsList.push(setting.setting_name);
            this.dropdownSettingsMultiple = setting.multiple;
          } else if (setting.settings_type == 'Switch_buttons') {
            const switches =
              this.graphForm.value.plot_name.plot_name.split('and');
            if (switches.length === 2) {
              const toggleOptions = ToggleButtonData[setting.setting_name];
              this.switchOptions.set(toggleOptions);
              this.setSelectedPlotVariant(
                setting.setting_name,
                selectedSettingForElement?.selected_setting_value ?? false,
              );
            }
          }
          this.setSelectedSettings(
            setting.setting_name,
            selectedSettingForElement?.selected_setting_value ?? false,
          );
        });

        this.aggregationList = res.data.aggregation_set?.map(
          (a_set: { aggregation_type: any }) => a_set.aggregation_type,
        );

        if (this.filterSmartBucket) {
          this.smartBucketingList = res?.data?.smartbucketing_set.filter(
            (item: { bucketing_type: string }) =>
              this.filterSmartBucketDataTypes.includes(
                item.bucketing_type.toUpperCase(),
              ),
          );
        } else {
          this.smartBucketingList = res.data.smartbucketing_set;
        }
      });
  }

  setDataTypes(types: string[]): string[] {
    const list: string[] = [];
    types.forEach(res => {
      switch (res) {
        case 'CATEGORICAL':
          list.push(...this.categoriesList);
          break;
        case 'NUMERICAL':
          list.push(...this.numericalList);
          break;
        case 'DATES':
          list.push(...this.DateList);
          break;

        default:
          break;
      }
    });
    return list;
  }

  get getSelectedOptions(): FormArray {
    return this.graphForm.get('selected_options') as FormArray;
  }

  get getSelectedSettings(): FormArray {
    return this.graphForm.get('selected_settings') as FormArray;
  }

  get selected_smart_bucketing(): FormArray {
    return this.graphForm.get('selected_smart_bucketing') as FormArray;
  }

  setSelectedOptions(
    option_name: string,
    isRequired = false,
    selected_aggregation: string | boolean = false,
    isMultiple = false,
    selected_column_name = '',
    isSmartBucketing = false,
    dataTypes: string[] | boolean = false,
    isDataList = false,
    isChild = false,
    nextIndex?: number,
  ): void {
    const group: Group = {
      option_name: option_name,
      selected_column_name: isRequired
        ? new FormControl(selected_column_name, [Validators.required])
        : selected_column_name,
      isMultiple: isMultiple,
      isRequired: isRequired,
    };
    if (selected_aggregation || typeof selected_aggregation == 'string') {
      group['selected_aggregation'] = isRequired
        ? new FormControl(selected_aggregation, [Validators.required])
        : (selected_aggregation as string);
    }
    if (isSmartBucketing) {
      this.isSmartBucketingEnabled = group['isSmartBucketing'] = true;
    }

    if (typeof dataTypes != 'boolean' && dataTypes.length) {
      group['columnOptions'] = this.formBuilder.array(
        isDataList ? [...dataTypes] : [...this.setDataTypes(dataTypes)],
      );
    }
    group['isChild'] = isChild;

    if (nextIndex === undefined) {
      this.getSelectedOptions.push(this.formBuilder.group(group));
    } else {
      this.getSelectedOptions.insert(nextIndex, this.formBuilder.group(group));
    }
  }

  setSelectedSettings(setting: string, setting_value: boolean): void {
    if (setting_value) {
      const settingsArray = this.formControlSettings.value;
      this.formControlSettings.setValue([...(settingsArray ?? []), setting]);
    }
    this.getSelectedSettings.push(
      this.formBuilder.group({
        setting_name: setting,
        selected_setting_value: setting_value,
      }),
    );
  }

  setSelectedPlotVariant(setting: string, setting_value: boolean) {
    const selectedPlotVariant = ToggleButtonData[setting];
    const preselectedPlotVariant = selectedPlotVariant.find(
      spv => spv.value === setting_value,
    );
    if (preselectedPlotVariant !== undefined)
      this.formControlPlotVariant.setValue(preselectedPlotVariant);
  }

  setPlotVariantSetting(event: MatButtonToggleChange) {
    const toggleValue: ToggleButtonDefinition = event.value;
    const selectedSettings = this.getSelectedSettings.value as {
      setting_name: string;
      selected_setting_value: boolean;
    }[];
    const settingPresent = selectedSettings.findIndex(
      s => s.setting_name === toggleValue.setting,
    );

    if (settingPresent !== -1) {
      this.getSelectedSettings
        .at(settingPresent)
        .get('selected_setting_value')
        ?.setValue(toggleValue.value);
      this.getSelectedSettings
        .at(settingPresent)
        .get('selected_setting_value')
        ?.updateValueAndValidity();
    }
  }

  setSetting(event: MatSelectChange): void {
    //TODO whole form needs refactoring - only did this that it works
    //Clean all settings
    this.getSelectedSettings.value.map(
      (setting: { setting_name: string; setting_value: string }) => {
        const index = this.getSelectedSettings.value.findIndex(
          (data: { setting_name: string }) =>
            data.setting_name === setting.setting_name,
        );
        if (index !== -1) {
          this.getSelectedSettings
            .at(index)
            .get('selected_setting_value')
            ?.setValue(false);
          this.getSelectedSettings
            .at(index)
            .get('selected_setting_value')
            ?.updateValueAndValidity();
        }
      },
    );

    //Set active Settings
    event.value.map((setting: string) => {
      const index = this.getSelectedSettings.value.findIndex(
        (data: { setting_name: string }) => data.setting_name === setting,
      );
      if (index !== -1) {
        this.getSelectedSettings
          .at(index)
          .get('selected_setting_value')
          ?.setValue(true);
        this.getSelectedSettings
          .at(index)
          .get('selected_setting_value')
          ?.updateValueAndValidity();
      }
    });
  }

  cancel(): void {
    this.cancelEvent.emit(true);
  }

  setSelectedButton(control: AbstractControl): void {
    this.getSwitchOptions.controls.forEach((element: AbstractControl) => {
      element.get('selected_button_value')!.setValue(element == control);
    });
  }

  get getSwitchOptions(): FormArray {
    return this.graphForm.get('switch_button_options') as FormArray;
  }

  setButtonSwitchOptions(
    switches: string[],
    setting_name: string,
    _selectedIndex = 0,
  ): void {
    this.graphForm.addControl(
      'switch_button_options',
      this.formBuilder.array([]),
    );
    switches.forEach(res => {
      this.getSwitchOptions.push(
        this.formBuilder.group({
          setting_name: setting_name,
          button_type: res.split(' ').join(''),
        }),
      );
    });
  }

  addNewOption(val: { option_name: string }, index: number): void {
    const currOptionsValue = [...this.getSelectedOptions.value];
    const currOpValuesWithOptionName = currOptionsValue.filter(
      (res: { option_name: string }) => res.option_name === val.option_name,
    );
    const firstOptVal = currOpValuesWithOptionName.at(0);
    const nextIndex = index + currOpValuesWithOptionName.length;

    this.setSelectedOptions(
      firstOptVal.option_name,
      firstOptVal.isRequired,
      [undefined, null].includes(firstOptVal?.selected_aggregation)
        ? false
        : '',
      false,
      '',
      firstOptVal.isSmartBucketing,
      firstOptVal?.columnOptions ? firstOptVal?.columnOptions : false,
      true,
      true,
      nextIndex,
    );
  }

  removeOption(index: number): void {
    this.getSelectedOptions.removeAt(index);
    this.getSelectedOptions.updateValueAndValidity();
    this.updateFilteredOptions();
  }

  submit(): void {
    if (this.graphForm.valid) {
      this.showValidationError = false;
      const formVal = this.graphForm.value;
      console.log(this.graphForm.value);

      if (this.isSmartBucketingEnabled) {
        if (this.selectedSmartBucketing === '') {
          this.showValidationError = true;
          return;
        }
      }

      const payload: CreatePlotPayload = {
        plot_name: formVal.plot_name.plot_name,
        selected_options: [],
        selected_settings: [],
        selected_smart_bucketing: [],
      };
      const checkEmptyAggr = formVal.selected_options.find(
        (val: {
          selected_column_name: string;
          selected_aggregation: string | boolean;
        }) =>
          val.selected_column_name &&
          Object.prototype.hasOwnProperty.call(val, 'selected_aggregation') &&
          !val.selected_aggregation,
      );
      if (checkEmptyAggr) {
        this.showValidationError = true;
        return;
      }
      payload['selected_options'] = formVal.selected_options
        .filter((check: { selected_column_name: string }) =>
          Boolean(check.selected_column_name),
        )
        .map(
          ({
            _isRequired,
            _isMultiple,
            _columnOptions,
            isSmartBucketing,
            ...res
          }: any) => {
            if (isSmartBucketing) {
              res['selected_smart_bucketing'] = this.selectedSmartBucketing
                ? true
                : false;
            }
            return res;
          },
        );

      payload['selected_settings'] = formVal.selected_settings.map(
        (res: { selected_setting_value: boolean }) => {
          return {
            ...res,
            selected_setting_value: res.selected_setting_value,
          };
        },
      );

      if (this.getSwitchOptions) {
        let switchName = '';
        const switchIndex = this.getSwitchOptions.value.findIndex(
          (data: { setting_name: string; selected_button_value: boolean }) => {
            switchName = data.setting_name;
            return data.selected_button_value === true;
          },
        );

        payload.selected_settings.push({
          setting_name: switchName,
          selected_setting_value: switchIndex === 0,
        });

        if (this.selectedSmartBucketing) {
          payload.selected_smart_bucketing.push({
            smart_bucketing_name:
              this.selectedSmartBucketing?.smart_bucketing_name,
            smart_bucketing_value:
              this.selectedSmartBucketing?.smart_bucketing_value,
          });
        }
      }
      const id = this.selectedFileID;
      this.exploreViewService
        .setNewPlot(id, payload)
        .pipe(takeUntil(this._unsubscribe))
        .subscribe(res => {
          this.toastrService.success(res.message);
          this.saveEvent.emit();
        });
    } else {
      this.showValidationError = true;
    }
  }

  updateFilteredOptions(): void {
    this.selectedOptionsList = [];
    this.sortByColName = [];
    this.getSelectedOptions.value.forEach(
      (val: { selected_column_name: string; option_name: string }) => {
        if (val.selected_column_name) {
          if (val.option_name === 'columns_of_interest') {
            this.sortByColName.push(val.selected_column_name);
          }
          this.selectedOptionsList.push(val.selected_column_name);
        }
      },
    );
    const sortColIndex = this.getSelectedOptions.value.findIndex(
      (options: { option_name: string }) =>
        options.option_name === 'sort_by_column_name',
    );
    if (
      sortColIndex != -1 &&
      !this.sortByColName.includes(
        this.getSelectedOptions.at(sortColIndex).value.selected_column_name,
      )
    ) {
      this.getSelectedOptions
        .at(sortColIndex)
        .get('selected_column_name')
        ?.setValue('');
    }
  }

  isFirstElementOfControlKind(
    op: AbstractControl<unknown>,
    optionName: string,
  ): boolean {
    const controls = this.getSelectedOptions?.controls;

    if (!controls) {
      return false;
    }

    const matchingIndices = controls
      .map((soc, index) =>
        soc.get('option_name')?.value === optionName ? index : -1,
      )
      .filter(index => index !== -1);

    const lowestIndex = Math.min(...matchingIndices);

    return controls[lowestIndex] === op;
  }

  ngOnDestroy(): void {
    this._unsubscribe.next(false);
    this._unsubscribe.complete();
  }
}
