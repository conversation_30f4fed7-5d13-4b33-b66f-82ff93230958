import { Component } from '@angular/core';
import { MatTabsModule } from '@angular/material/tabs';
import { DeployedEnvironmentsContainerComponent } from '../deployed-environments-container/deployed-environments-container.component';
import { ApiQuickstartContainerComponent } from '../api-quickstart-container/api-quickstart-container.component';

@Component({
  selector: 'app-tab-container',
  imports: [
    MatTabsModule,
    DeployedEnvironmentsContainerComponent,
    ApiQuickstartContainerComponent,
  ],
  templateUrl: './tab-container.component.html',
  styleUrl: './tab-container.component.css',
})
export class TabContainerComponent {}
