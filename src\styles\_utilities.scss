/* ========================================
   THEME-AWARE UTILITY CLASSES
   Using --md-sys-* tokens for custom components (from _color.scss)
   ======================================== */

// User clicks toggle → ThemeService updates NgRx store →
// CSS class changes on <body> → CSS custom properties switch →
// Utility classes automatically use new colors → UI updates instantly

// Background utilities - semantic naming
.bg-page {
  background-color: var(--md-sys-color-surface-container-lowest);
}

.bg-card {
  background-color: var(--md-sys-color-surface-container-lowest);
}

.bg-elevated {
  background-color: var(--md-sys-color-surface-container-high);
}

.bg-button-primary {
  background-color: var(--md-sys-color-primary);
}

.bg-button-secondary {
  background-color: var(--md-sys-color-surface-container-highest);
}

// Text color utilities - semantic naming
.text-primary {
  color: var(--md-sys-color-on-surface);
}

.text-secondary {
  color: var(--md-sys-color-on-surface-variant);
}

.text-accent {
  color: var(--md-sys-color-primary);
}

.text-on-primary {
  color: var(--md-sys-color-on-primary);
}

.text-on-dark {
  color: var(--md-sys-color-on-primary-container);
}

// Border utilities - semantic naming
.border-subtle {
  border-color: var(--md-sys-color-outline-variant);
}

.border-normal {
  border-color: var(--md-sys-color-outline);
}

// Icon color utilities - semantic naming
.icon-accent {
  color: var(--md-sys-color-primary);
}

.icon-primary {
  color: var(--md-sys-color-on-surface);
}

.icon-secondary {
  color: var(--md-sys-color-on-surface-variant);
}

.icon-on-primary {
  color: var(--md-sys-color-on-primary);
}

/* High specificity to override Material Design defaults */
button.mat-mdc-icon-button.header-icon-button,
.header-icon-button {
  color: var(--md-sys-color-primary) !important;
  background-color: var(--md-sys-color-surface-container-lowest) !important;
  border: none !important;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: var(--md-sys-color-surface-container-low) !important;
  }

  /* Ensure the icon inside also gets the right color */
  .mat-icon {
    color: var(--md-sys-color-primary) !important;
  }
}

/* Dark mode styling for header icon buttons */
:host-context(.dark-mode) button.mat-mdc-icon-button.header-icon-button,
:host-context(.dark-mode) .header-icon-button {
  background-color: #181c20 !important;

  &:hover {
    background-color: #181c20 !important;
  }
}
