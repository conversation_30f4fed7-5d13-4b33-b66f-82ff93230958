import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, firstValueFrom } from 'rxjs';
import { environment } from '../../../env/env';
import {
  CodeSnippetResponseData,
  DlInferenceCodeSnippetBodyPayload,
} from '../models/api-inference-code-snippet.model';
import { CustomResponse } from '../../../core/models/common-backend.model';
import { GetDlInferenceJobApiResponseData } from '../models/api-inference-deployment.model';

@Injectable({
  providedIn: 'root',
})
export class ApiInferenceService {
  constructor(private http: HttpClient) {}

  async getQuickStartCodeSnippet(
    training_id: string,
  ): Promise<CustomResponse<CodeSnippetResponseData>> {
    return firstValueFrom(this.getQuickStartCodeSnippetObservable(training_id));
  }

  async getPredictCodeSnippetForInference(
    dlInferenceId: string,
  ): Promise<CustomResponse<CodeSnippetResponseData>> {
    return firstValueFrom(
      this.getPredictCodeSnippetForInferenceObservable(dlInferenceId),
    );
  }

  async getStopCodeSnippetForInference(
    dlInferenceId: string,
  ): Promise<CustomResponse<CodeSnippetResponseData>> {
    return firstValueFrom(
      this.getStopCodeSnippetForInferenceObservable(dlInferenceId),
    );
  }

  async getStatusCodeSnippetForInference(
    dlInferenceId: string,
  ): Promise<CustomResponse<CodeSnippetResponseData>> {
    return firstValueFrom(
      this.getStatusCodeSnippetForInferenceObservable(dlInferenceId),
    );
  }

  async starDlInferenceInstance(
    training_id: string,
    payload: DlInferenceCodeSnippetBodyPayload,
  ): Promise<CustomResponse<unknown>> {
    // TO-DO : Define model/interface for response
    try {
      return await firstValueFrom(
        this.starDlInferenceInstanceObservable(training_id, payload),
      );
    } catch (error) {
      console.error('Service error:', error);
      throw error;
    }
  }

  async stopDlInferenceInstance(
    inferenceInstanceId: string,
  ): Promise<CustomResponse<unknown>> {
    // TO-DO : Define Model for Response. Its unknown for now
    return firstValueFrom(
      this.stopDlInferenceInstanceObservable(inferenceInstanceId),
    );
  }

  async getApiDlInferenceInstances(
    training_id: string,
  ): Promise<CustomResponse<GetDlInferenceJobApiResponseData>> {
    return firstValueFrom(
      this.getApiDlInferenceInstancesObservable(training_id),
    );
  }

  // Optional: Observable-based methods for more Angular-idiomatic usage
  getQuickStartCodeSnippetObservable(
    training_id: string,
  ): Observable<CustomResponse<CodeSnippetResponseData>> {
    const url = `${environment.apiUrl}dlmodels/api/v1/inference-snippets/${training_id}/start`;

    return this.http.get<CustomResponse<CodeSnippetResponseData>>(url);
  }

  getPredictCodeSnippetForInferenceObservable(
    dlInferenceId: string,
  ): Observable<CustomResponse<CodeSnippetResponseData>> {
    const url = `${environment.apiUrl}dlmodels/api/v1/inference-snippets/${dlInferenceId}/predict`;

    return this.http.get<CustomResponse<CodeSnippetResponseData>>(url);
  }

  getStopCodeSnippetForInferenceObservable(
    dlInferenceId: string,
  ): Observable<CustomResponse<CodeSnippetResponseData>> {
    const url = `${environment.apiUrl}dlmodels/api/v1/inference-snippets/${dlInferenceId}/stop`;

    return this.http.get<CustomResponse<CodeSnippetResponseData>>(url);
  }

  getStatusCodeSnippetForInferenceObservable(
    dlInferenceId: string,
  ): Observable<CustomResponse<CodeSnippetResponseData>> {
    const url = `${environment.apiUrl}dlmodels/api/v1/inference-snippets/${dlInferenceId}/status`;

    return this.http.get<CustomResponse<CodeSnippetResponseData>>(url);
  }

  starDlInferenceInstanceObservable(
    training_id: string,
    payload: DlInferenceCodeSnippetBodyPayload,
  ): Observable<CustomResponse<unknown>> {
    const url = `${environment.apiUrl}dlmodels/api/v1/dl-inference/${training_id}/`;

    return this.http.post<CustomResponse<unknown>>(url, payload);
  }

  stopDlInferenceInstanceObservable(
    inferenceInstanceId: string,
  ): Observable<CustomResponse<unknown>> {
    const url = `${environment.apiUrl}dlmodels/api/v1/dl-inference/${inferenceInstanceId}/instance`;

    return this.http.delete<CustomResponse<unknown>>(url);
  }

  getApiDlInferenceInstancesObservable(
    training_id: string,
  ): Observable<CustomResponse<GetDlInferenceJobApiResponseData>> {
    const url = `${environment.apiUrl}dlmodels/api/v1/dl-inference/${training_id}/?cursor=0&limit=10`;

    return this.http.get<CustomResponse<GetDlInferenceJobApiResponseData>>(url);
  }
}
