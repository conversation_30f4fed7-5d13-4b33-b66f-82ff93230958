<div class="dialog-header">
  <div
    class="flex justify-between items-center object-center dark:text-white"
    [class.pb-4]="stepper.selectedIndex === 0">
    <h2 mat-dialog-title>
      {{ 'New Training | ' + getFormTitle(stepper.selectedIndex) }}
    </h2>
    <button
      mat-icon-button
      (click)="onNoClick()"
      class="text-setSettinggray-400 hover:text-gray-600">
      <mat-icon>close</mat-icon>
    </button>
  </div>
  <!-- Training name display for step 2 and 3 -->
  @if (stepper.selectedIndex > 0) {
    <p class="training-name dark:text-white">
      {{ trainingForm.get('name')?.value }}
    </p>
  }
</div>

<!-- Horizontal divider line - only show for step 1 -->
@if (stepper.selectedIndex === 0) {
  <div class="dialog-divider"></div>
}

<div class="dialog-content h-full">
  <mat-dialog-content class="h-full">
    <mat-stepper #stepper class="h-full">
      <mat-step class="h-full">
        <div class="flex flex-col h-full">
          <!-- Scrollable form content -->
          <div class="flex-grow overflow-y-auto pr-2">
            <app-step-1 [trainingForm]="trainingForm"></app-step-1>
          </div>

          <!-- Sticky footer navigation -->
          <div class="flex justify-between items-center mt-auto pt-4">
            <button mat-button (click)="onNoClick()">Cancel</button>
            <span class="text-sm text-gray-600 font-medium">
              Step {{ stepper.selectedIndex + 1 }} of 3
            </span>
            <button
              mat-flat-button
              matStepperNext
              [disabled]="
                trainingForm.get('name')?.invalid ||
                trainingForm.get('model')?.invalid
              ">
              Next
            </button>
          </div>
        </div>
      </mat-step>
      <mat-step class="h-full">
        <div class="flex flex-col h-full">
          <!-- Scrollable form content -->
          <div class="flex-grow overflow-y-auto pr-2">
            <app-step-2 [trainingForm]="trainingForm"></app-step-2>
          </div>

          <!-- Sticky footer navigation -->
          <div class="flex justify-between items-center">
            <button mat-button matStepperPrevious>Back</button>
            <span>Step {{ stepper.selectedIndex + 1 }} of 3</span>
            <button
              mat-flat-button
              matStepperNext
              [disabled]="trainingForm.get('assets')?.invalid"
              (click)="populateConfigs()">
              Next
            </button>
          </div>
        </div>
      </mat-step>

      <mat-step class="h-full">
        <div class="flex flex-col h-full">
          <!-- Scrollable form content -->
          <div class="flex-grow overflow-y-auto pr-2">
            <app-step-3 [trainingForm]="trainingForm"></app-step-3>
          </div>

          <!-- Sticky footer navigation -->
          <div class="flex justify-between items-center pt-4">
            <button mat-button matStepperPrevious>Back</button>
            <span>Step {{ stepper.selectedIndex + 1 }} of 3</span>
            <button
              mat-flat-button
              matStepperNext
              [disabled]="trainingForm.invalid"
              (click)="createTraining($event)">
              Create Training
            </button>
          </div>
        </div>
      </mat-step>
      <mat-step> </mat-step>
    </mat-stepper>
  </mat-dialog-content>
</div>
