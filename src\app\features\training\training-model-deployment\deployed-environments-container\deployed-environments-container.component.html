<div class="p-3">
  @if (this.trainingAppStore.deployedInference().instances.length !== 0) {
    <div class="bg-[#F7F9FF]">
      <!-- HEADER -->
      <div class="py-4">
        <p class="font-bold">Deployed Environments</p>
      </div>
      <!-- Card Container -->
      <div class="flex flex-col gap-2 py-4">
        @for (
          instance of this.trainingAppStore.deployedInference().instances;
          track instance
        ) {
          <app-inference-instance-card
            [instanceInfo]="instance"></app-inference-instance-card>
        }
      </div>
    </div>
  } @else {
    <app-empty-state-card
      title="No Deployed Environments"
      description="You have not deployed any environments yet. Deploy your AI models to start using them in production."
      icon="rocket_launch"
      buttonText="Deploy Now">
    </app-empty-state-card>
  }
</div>
