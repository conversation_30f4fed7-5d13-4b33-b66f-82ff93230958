import { FormBuilder, Validators } from '@angular/forms';

export const ConfigTypes = [
  'training_config',
  'model_config',
  'feature_config',
  'generation_config',
];

export const DLTrainingFormFields = (fb: FormBuilder, test_size: number) => ({
  name: ['', Validators.required],
  category: [''],
  subcategory: [''],
  model: ['', Validators.required],
  train_size: [100 - test_size, [Validators.min(5), Validators.max(95)]],
  test_size: [test_size, [Validators.min(5), Validators.max(95)]],
  training_config: fb.array([]),
  model_config: fb.array([]),
  feature_config: fb.array([]),
  generation_config: fb.array([]),
  assets: fb.group([]),
});

export const getDataType = (dataType: string) => {
  if (dataType === 'int' || dataType === 'float') return 'number';
  else return 'text';
};
