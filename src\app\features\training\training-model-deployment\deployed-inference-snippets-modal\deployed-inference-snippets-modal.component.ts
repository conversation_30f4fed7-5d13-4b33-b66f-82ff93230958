import { Component, Inject, OnInit } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import {
  MAT_DIALOG_DATA,
  MatDialogModule,
  MatDialogRef,
} from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { MatTabsModule } from '@angular/material/tabs';

import { DlInferenceInstance } from '../../models/api-inference-deployment.model';
import { ApiInferenceService } from '../../service/api-inference.service';
import { CodeSnippetMeta } from '../../models/api-inference-code-snippet.model';
import { MultiLangSnippetContainerComponent } from '../multi-lang-snippet-container/multi-lang-snippet-container.component';

@Component({
  selector: 'app-deployed-inference-snippets-modal',
  imports: [
    MatButtonModule,
    MatIconModule,
    MatButtonModule,
    MatDialogModule,
    MatTabsModule,
    MultiLangSnippetContainerComponent,
  ],
  templateUrl: './deployed-inference-snippets-modal.component.html',
  styleUrl: './deployed-inference-snippets-modal.component.css',
})
export class DeployedInferenceSnippetsModalComponent implements OnInit {
  predictCodeSnippetForInference!: CodeSnippetMeta[];
  stopCodeSnippetForInference!: CodeSnippetMeta[];
  statusCodeSnippetForInference!: CodeSnippetMeta[];

  constructor(
    private popupRef: MatDialogRef<DeployedInferenceSnippetsModalComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { instanceInfo: DlInferenceInstance },
    private apiInferenceService: ApiInferenceService,
  ) {}

  ngOnInit(): void {
    if (this.data.instanceInfo) {
      this.apiInferenceService
        .getPredictCodeSnippetForInference(String(this.data.instanceInfo.id))
        .then(res => {
          this.predictCodeSnippetForInference = (res.data?.results ??
            []) as CodeSnippetMeta[];
        })
        .catch(err => {
          console.error(err);
        });
      this.apiInferenceService
        .getStopCodeSnippetForInference(String(this.data.instanceInfo.id))
        .then(res => {
          this.stopCodeSnippetForInference = (res.data?.results ??
            []) as CodeSnippetMeta[];
        })
        .catch(err => {
          console.error(err);
        });
      this.apiInferenceService
        .getStatusCodeSnippetForInference(String(this.data.instanceInfo.id))
        .then(res => {
          this.statusCodeSnippetForInference = (res.data?.results ??
            []) as CodeSnippetMeta[];
        })
        .catch(err => {
          console.error(err);
        });
    }
  }

  closeModal() {
    this.popupRef.close();
  }
}
