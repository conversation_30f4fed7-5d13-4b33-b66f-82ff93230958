/* eslint-disable @typescript-eslint/no-explicit-any */

import {
  Component,
  ElementRef,
  inject,
  <PERSON><PERSON><PERSON><PERSON>,
  OnInit,
  ViewChild,
  AfterViewInit,
  effect,
  computed,
} from '@angular/core';
import { MatIcon } from '@angular/material/icon';
import { SharedModule } from '../../../shared/shared.module';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { TrainingModelCardsComponent } from './training-model-cards/training-model-cards.component';
import { MatDialog } from '@angular/material/dialog';
import { TrainingDialogComponent } from '../training-dialog/training-dialog.component';
import {
  MlProjectBasedOnTimeline,
  mlProjectInitialState,
  MLTrainingProjectInfo,
  TimelineInterface,
} from '../models/training.model';
import { ToastrService } from 'ngx-toastr';
import { Subscription } from 'rxjs';
import { EmptyStateCardComponent } from '../../../shared/components/empty-state-card/empty-state-card.component';
import { ProjectService } from '../../../core/services/project.service';
import { ProjectData } from '../../../features/dashborad/models/project.model';
import { BackendResponse } from '../../../_models/visual-data/visual-data.model';
import { TrainingStore } from '../store/training.store';

@Component({
  selector: 'app-training-langing-page',
  imports: [
    MatIcon,
    SharedModule,
    CommonModule,
    MatButtonModule,
    TrainingModelCardsComponent,
    EmptyStateCardComponent,
  ],
  templateUrl: './training-langing-page.component.html',
  styleUrl: './training-langing-page.component.css',
})
export class TrainingLangingPageComponent
  implements OnInit, OnDestroy, AfterViewInit
{
  @ViewChild('scrollContainer', { static: true }) scrollContainer!: ElementRef;
  @ViewChild('sentinel') sentinel!: ElementRef;

  readonly dialog = inject(MatDialog);
  now = new Date();
  mlProjects!: MlProjectBasedOnTimeline;
  trainings!: any;
  observer!: IntersectionObserver;
  subscriptions: Subscription = new Subscription();
  project_id = localStorage.getItem('project_id');
  projectId = 0;
  projectName: BackendResponse<ProjectData> | undefined;
  isTrainingEmpty = true;
  mlProjectInitialState = {
    today: [],
    last_week: [],
    last_month: [],
    last_year: [],
  };
  timeframe = ['today', 'last_week', 'last_month', 'last_year'];
  pageSize = 10;
  timeline: TimelineInterface = {
    today: {
      title: 'Today',
      dateRange: `${this.formatDate(this.now)}`,
    },
    last_week: {
      title: 'Last Week',
      dateRange: `${this.getLastWeekRange()}`,
    },
    last_month: {
      title: 'Last Month',
      dateRange: `${this.getLastMonth()}`,
    },
    last_year: {
      title: 'Last year',
      dateRange: `${this.getLastYear()}`,
    },
  };
  readonly store = inject(TrainingStore);
  isLoading = computed(() => this.store.trainings().isLoading);

  constructor(
    private toasterService: ToastrService,
    private projectService: ProjectService,
  ) {}

  ngOnInit(): void {
    if (this.project_id) {
      this.projectId = Number(this.project_id);
    }
    this.store.getDlModelTrainings(this.projectId);

    const projNameSubscription = this.projectService
      .getProjectName(this.projectId)
      .subscribe({
        next: (res: any) => {
          this.projectName = res.data?.title;
        },
        error: error => {
          this.toasterService.error(error);
        },
      });

    this.subscriptions.add(projNameSubscription);
  }

  ngAfterViewInit() {
    this.initIntersectionObserver();
  }

  ngOnDestroy(): void {
    this.store.resetAll();
    this.subscriptions.unsubscribe();
  }

  // Checks if the user has scrolled to the bottom of the page
  initIntersectionObserver() {
    this.observer = new IntersectionObserver(
      entries => {
        if (entries[0].isIntersecting) {
          this.loadMoreProjects();
        }
      },
      {
        root: this.scrollContainer.nativeElement,
        threshold: 1.0,
      },
    );
    this.observer.observe(this.sentinel.nativeElement);
  }

  // Loads data for infinite scroll pagination
  loadMoreProjects() {
    const total = this.store.totalProjects();
    if (total > this.pageSize) {
      this.pageSize = this.pageSize + 10;
      this.store.getDlModelTrainings(this.projectId, this.pageSize); //pass the page size alongwith that
    }
  }

  // Loads the list of ML trainings
  onEffect = effect(() => {
    this.trainings = this.store
      .trainings()
      .list.reduce((acc: any, item: any) => {
        const dateKey = new Date(item.created_at).toISOString().split('T')[0]; // Extract date (YYYY-MM-DD)
        if (!acc[dateKey]) {
          acc[dateKey] = [];
        }
        acc[dateKey].push(item);
        return acc;
      }, {});
    this.segregateByTime(this.store.trainings().list);
  });

  // Segregates ML trainings based on the timeline: today, last week, last month, last year
  segregateByTime(data: MLTrainingProjectInfo[]) {
    const now = new Date();
    this.mlProjects = {
      today: [],
      last_week: [],
      last_month: [],
      last_year: [],
    };

    data.forEach((item: MLTrainingProjectInfo) => {
      const entryDate = new Date(item.updated_at);
      const entryDateMidnight = new Date(
        entryDate.setHours(0, 0, 0, 0),
      ).getTime();
      const nowMidnight = new Date(now.setHours(0, 0, 0, 0)).getTime();
      const diffDays =
        (nowMidnight - entryDateMidnight) / (1000 * 60 * 60 * 24);
      switch (true) {
        case diffDays === 0:
          this.mlProjects.today.push(item);
          break;
        case diffDays > 0 && diffDays <= 7:
          this.mlProjects.last_week.push(item);
          break;
        case entryDate.getMonth() === now.getMonth() - 1 &&
          entryDate.getFullYear() === now.getFullYear(): // Last month
          this.mlProjects.last_month.push(item);
          break;
        default:
          this.mlProjects.last_year.push(item);
          break;
      }
      this.isTrainingEmpty =
        JSON.stringify(this.mlProjects) ===
        JSON.stringify(mlProjectInitialState)
          ? true
          : false;
    });
  }

  openDialog(): void {
    const dialogRef = this.dialog.open(TrainingDialogComponent, {});
    dialogRef.afterClosed().subscribe(result => {
      if (result == true) return;
      // this.getMlProjects(this.pageSize);
      this.store.getDlModelTrainings(this.projectId);
      if (result?.success) {
        console.log('Project updated successfully:', result.updatedData);
      } else {
        console.log('Dialog closed without updates.');
      }
    });
  }

  updateTraining(event: boolean) {
    if (event) this.store.getDlModelTrainings(this.projectId);
  }
  getTimelineTitle(time: string): string {
    return this.timeline[time as keyof TimelineInterface]?.title || '';
  }

  getTimelineDateRange(time: string): string {
    return this.timeline[time as keyof TimelineInterface]?.dateRange || '';
  }

  getMlProjectAsPerTimeline(time: string): MLTrainingProjectInfo[] {
    return this.mlProjects[time as keyof TimelineInterface];
  }

  formatDate(date: Date) {
    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const year = date.getFullYear();
    return `${day}.${month}.${year}`;
  }

  getLastWeekRange() {
    const today = new Date();
    const lastWeekStart = new Date(today);
    const lastWeekEnd = new Date(today);

    lastWeekStart.setDate(today.getDate() - 7);

    return `${this.formatDate(lastWeekStart)} - ${this.formatDate(lastWeekEnd)}`;
  }

  getLastMonth() {
    const lastMonth = new Date(this.now);
    lastMonth.setMonth(this.now.getMonth() - 1);
    return lastMonth.toLocaleString('default', { month: 'long' });
  }

  getLastYear() {
    return (this.now.getFullYear() - 1).toString();
  }
}
