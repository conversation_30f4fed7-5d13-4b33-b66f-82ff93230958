<div class="hidden md:block">
  <mat-select
    [(ngModel)]="selectedSortOption.value"
    panelClass="custom-dropdown-panel"
    class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block p-2.5 w-[230px] dark:bg-[#181c20] dark:text-white">
    <mat-select-trigger class="w-[230px] flex items-center">
      <ng-container
        class=""
        *ngIf="
          selectedSortOption && selectedSortOption.value !== 'initialSelect';
          else placeholder
        ">
        <button
          mat-icon-button
          (click)="sortData($event)"
          matTooltip="Sort ascending/descending">
          <mat-icon class="mx-2">swap_vert</mat-icon>
        </button>
        <span>{{ 'Sorted by ' + selectedSortOption.title }}</span>
      </ng-container>
      <ng-template #placeholder> Select an option to sort </ng-template>
    </mat-select-trigger>
    <!-- Show the 'Select an option to sort' only if no option is selected -->
    <mat-option
      *ngIf="
        !selectedSortOption || selectedSortOption.value === 'initialSelect'
      "
      value="initialSelect"
      disabled>
      Select an option to sort
    </mat-option>

    <!-- Show sorting options -->
    @for (option of sortDropdownOptions; track option) {
      <mat-option
        [value]="option.value"
        (click)="onSortOptionChange(selectedSortOption.value)">
        <span>{{ 'Sorted by ' + option.title }}</span>
        <mat-icon>{{ option.icon }}</mat-icon>
      </mat-option>
    }

    <!-- Show 'Remove sort' only if an option is selected -->
    <mat-option
      *ngIf="selectedSortOption && selectedSortOption.value !== 'initialSelect'"
      value="initialSelect"
      (click)="onSortOptionChange(selectedSortOption.value)">
      Remove sort
    </mat-option>
  </mat-select>
</div>
