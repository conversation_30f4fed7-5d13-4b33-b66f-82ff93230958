/* eslint-disable @typescript-eslint/no-explicit-any */

import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from '../../../env/env';
import { Observable } from 'rxjs';
import {
  DlModelsList,
  DlTrainingPayload,
  StartMlTrainingResponse,
  TrainingOverviewInterface,
} from '../models/training.model';
import { Router } from '@angular/router';

@Injectable({
  providedIn: 'root',
})
export class TrainingService {
  constructor(
    private http: HttpClient,
    private router: Router,
  ) {}
  private getHeaders(): HttpHeaders {
    const token = localStorage.getItem('access_token');
    return new HttpHeaders({
      Authorization: `Bearer ${token || ''}`,
      'Content-Type': 'application/json',
    });
  }

  startMlTraining(model_id: number): Observable<StartMlTrainingResponse> {
    return this.http.post<StartMlTrainingResponse>(
      `${environment.apiUrl}dlmodels/dltraining/${model_id}/train`,
      {},
      { headers: this.getHeaders() },
    );
  }

  getMlTrainingInfo(id: number): Observable<TrainingOverviewInterface> {
    return this.http.get<TrainingOverviewInterface>(
      `${environment.apiUrl}dlmodels/dltraining/${id}/`,
      {
        headers: this.getHeaders(),
      },
    );
  }

  getFileList(project_id: number): Observable<any> {
    const headers = this.getHeaders();
    return this.http.get<any>(
      `${environment.apiUrl}projects/${project_id}/files/filter/?search_files=true&file_type=non_image`,
      { headers },
    );
  }

  getTrainingsResults(id: number): Observable<any> {
    const headers = this.getHeaders();

    return this.http.get<any>(
      `${environment.apiUrl}dlmodels/dltraining/${id}/results`,
      { headers },
    );
  }

  startNewInference(trainingId: number) {
    return this.http.post<any>(
      `${environment.apiUrl}dlmodels/dlinference/${trainingId}/`,
      {},
      { headers: this.getHeaders() },
    );
  }
  getMlInferences(trainingId: number) {
    const headers = this.getHeaders();

    return this.http.get<any>(
      `${environment.apiUrl}dlmodels/dlinference/${trainingId}/`,
      { headers },
    );
  }

  checkRunningInference(trainingId: number) {
    const headers = this.getHeaders();

    return this.http.get<any>(
      `${environment.apiUrl}dlmodels/dlinference/check-running/${trainingId}/`,
      { headers },
    );
  }

  getTraningPredictions(payload: any, trainingId: number) {
    return this.http.post<any>(
      `${environment.apiUrl}dlmodels/dlinference/${trainingId}/prediction`,
      { features: payload.features },
      { headers: this.getHeaders() },
    );
  }
  stopMlInference(ml_inference_id: number) {
    return this.http.delete<any>(
      `${environment.apiUrl}dlmodels/dlinference/${ml_inference_id}/instance`,
    );
  }

  stopTrainModel(dl_training_id: number) {
    return this.http.delete<any>(
      `${environment.apiUrl}dlmodels/dltraining/${dl_training_id}/train`,
    );
  }

  getDLModels(category = '', subcategory = '', name = '') {
    return this.http.get<DlModelsList>(
      `${environment.apiUrl}dlmodels/dlmodels/?category=${category}&subcategory=${subcategory}&name=${name}`,
    );
  }

  getFeaturesTarget(file_id: number, model_id: number) {
    return this.http.get<any>(
      `${environment.apiUrl}dlmodels/files/${file_id}/features-targets/?model_id=${model_id}`,
    );
  }

  createDlTraining(payload: DlTrainingPayload) {
    return this.http.post<any>(
      `${environment.apiUrl}dlmodels/dltraining/`,
      payload,
    );
  }

  getDLModelTrainings(project_id: number, pageSize = 10): Observable<any> {
    return this.http.get<any>(
      `${environment.apiUrl}dlmodels/${project_id}/trainings/?limit=${pageSize}`,
      {
        headers: this.getHeaders(),
      },
    );
  }

  getTrainingIdFromUrl(): string | null | undefined {
    const snapshot = this.router.routerState.snapshot;
    let currentRoute = snapshot.root;
    let trainingId = null;

    while (currentRoute.firstChild) {
      if (
        currentRoute.routeConfig?.path === 'training-results/:id' &&
        currentRoute.params['id'] !== undefined
      ) {
        // Found the training results route
        break;
      }
      currentRoute = currentRoute.firstChild;
    }
    trainingId = currentRoute.params['id'];
    return trainingId;
  }
}
