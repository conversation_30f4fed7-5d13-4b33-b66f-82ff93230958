import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  inject,
  <PERSON><PERSON><PERSON><PERSON>,
  OnInit,
} from '@angular/core';
import {
  Form<PERSON>rray,
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import {
  MAT_DIALOG_DATA,
  MatDialogContent,
  MatDialogRef,
} from '@angular/material/dialog';
import { MatSelectChange } from '@angular/material/select';
import { MatStepperModule } from '@angular/material/stepper';
import { TrainingService } from '../service/training-service.service';
import { ToastrService } from 'ngx-toastr';
import {
  DLConfiguration,
  DlTrainingPayload,
  FileFolderSelection,
  MLTrainingProjectInfo,
  TargetFeatureInterface,
} from '../models/training.model';
import { Subscription } from 'rxjs';
import { SharedModule } from '../../../shared/shared.module';
import { MatIconModule } from '@angular/material/icon';
import { MatExpansionModule } from '@angular/material/expansion';
import { TrainingStore } from '../store/training.store';
import {
  ConfigTypes,
  DLTrainingFormFields,
  getDataType,
} from '../training.const';
import { Step1Component } from './step-1/step-1.component';
import { Step2Component } from './step-2/step-2.component';
import { Step3Component } from './step-3/step-3.component';
@Component({
  selector: 'app-training-dialog',
  imports: [
    SharedModule,
    MatDialogContent,
    MatStepperModule,
    ReactiveFormsModule,
    MatIconModule,
    MatExpansionModule,
    Step1Component,
    Step2Component,
    Step3Component,
    MatButtonModule,
  ],
  templateUrl: './training-dialog.component.html',
  styleUrl: './training-dialog.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TrainingDialogComponent implements OnDestroy, OnInit {
  readonly dialogRef = inject(MatDialogRef<TrainingDialogComponent>);
  readonly data = inject<MLTrainingProjectInfo>(MAT_DIALOG_DATA);
  readonly store = inject(TrainingStore);
  subscriptions: Subscription = new Subscription();
  trainingForm!: FormGroup;
  // previewData: any;
  categoriesList: {
    categories: string[];
    subcategories: string[];
    model: string[];
  } = {
    categories: [],
    subcategories: [],
    model: [],
  };
  dlConfig!: {
    training_config: DLConfiguration[];
    generation_config: DLConfiguration[];
    model_config: DLConfiguration[];
    feature_config: DLConfiguration[];
  };
  featureAndTargets: {
    features: TargetFeatureInterface[];
    targets: TargetFeatureInterface[];
  } = {
    features: [],
    targets: [],
  };
  assets: { key: string; display_name: string }[] = [];
  configTypes = ConfigTypes;

  constructor(
    private formBuilder: FormBuilder,
    private trainingService: TrainingService,
    private toasterService: ToastrService,
    private cdr: ChangeDetectorRef,
  ) {}
  test_size = 10;
  projectId = Number(localStorage.getItem('project_id'));
  ngOnInit(): void {
    this.store.loadDlModels();
    this.trainingForm = this.formBuilder.group(
      DLTrainingFormFields(this.formBuilder, this.test_size),
    );
    this.trainingForm.controls['train_size'].valueChanges.subscribe(val => {
      this.trainingForm.patchValue(
        {
          train_size: val,
          test_size: 100 - val,
        },
        { emitEvent: false },
      );
    });
    this.trainingForm.controls['test_size'].valueChanges.subscribe(val => {
      this.trainingForm.patchValue(
        {
          train_size: 100 - val,
          test_size: val,
        },
        { emitEvent: false },
      );
    });
  }

  ngOnDestroy(): void {
    this.store.setResetState();
    this.subscriptions.unsubscribe();
  }

  // Populates the training form if it is an edit action
  populateTrainingForm() {
    // if (this.data) {
    //   this.trainingForm.patchValue({
    //     name: this.data?.name,
    //     trainingData: this.data?.file?.id,
    //     selectedTarget: this.data?.selected_target.id,
    //     selectedFeatures: this.data?.selected_features.map(
    //       feature => feature.id,
    //     ),
    //     test_size: this.data?.test_size * 100,
    //     train_size: 100 - this.data?.test_size * 100,
    //   });
    // }
    this.cdr.detectChanges();
  }

  // To create ML MOdel training
  createTraining(event: MouseEvent) {
    event.stopPropagation();
    const flat_configuration = this.configTypes.flatMap(key => {
      const configs = this.trainingForm.get(key)?.value ?? [];
      return configs
        .filter((config: DLConfiguration) => config.value !== '')
        .map((config: DLConfiguration) => ({
          name: config.name,
          config_type: config.config_type,
          value:
            getDataType(config.data_type) === 'number'
              ? Number(config.value)
              : config.value,
        }));
    });
    const payload: DlTrainingPayload = {
      name: this.trainingForm.get('name')?.value,
      model_id: this.store.selectedModel().modelId,
      project: localStorage.getItem('project_id') ?? '',
      test_size: 0.3,
      validation_size: 0.1,
      assets: Object.values(this.trainingForm.get('assets')?.value).flat(),
      configurations: flat_configuration,
    };
    this.subscriptions.add(
      this.trainingService.createDlTraining(payload).subscribe({
        next: res => {
          this.toasterService.success(res.data.message);
          this.store.setResetState();
          this.dialogRef.close();
        },
        error: () => {
          this.store.setResetState();
          this.dialogRef.close();
        },
      }),
    );
  }

  onNoClick(): void {
    this.store.setResetState();
    this.dialogRef.close(true);
  }

  getSelectedFolder(event: FileFolderSelection, asset_key: string) {
    const selectedFileFolder = event.checked
      ? {
          key: asset_key,
          [event.type === 'folder' ? 'folder_path' : 'file_id']:
            event.id === 0 ? event.path.slice(1) : event.id,
        }
      : {};
    const assetsFormGroup = this.trainingForm.get('assets') as FormGroup;
    if (!assetsFormGroup.contains(asset_key)) {
      assetsFormGroup.addControl(asset_key, this.formBuilder.control([]));
    }
    assetsFormGroup.get(asset_key)?.setValue(selectedFileFolder);
  }

  getModelsByFilter(
    filterKey: 'category' | 'subcategory' | 'model',
    filterValue: MatSelectChange,
  ) {
    this.store.setSelectedModel(filterKey, filterValue.value);
    this.cdr.markForCheck();
  }

  getConfigArray(type: string): FormArray {
    return this.trainingForm.get(type) as FormArray;
  }

  populateConfigs(): void {
    this.store.setTrainingConfigurations();
    this.dlConfig = {
      training_config: this.store.training_config(),
      feature_config: this.store.feature_config(),
      generation_config: this.store.generation_config(),
      model_config: this.store.model_config(),
    };
    this.configTypes.forEach(type => {
      const array = this.getConfigArray(type);
      array.clear();

      (this.dlConfig[type as keyof typeof this.dlConfig] || []).forEach(
        (cfg: DLConfiguration) => {
          array.push(
            this.formBuilder.group({
              name: [cfg.name],
              placeholder: cfg.default_value,
              value: ['', cfg.required ? Validators.required : []],
              data_type: [cfg.data_type],
              description: [cfg.description],
              config_type: type,
              required: cfg.required,
            }),
          );
        },
      );
    });
  }

  getFormTitle(index: number): string {
    switch (index) {
      case 0:
        return 'Model Selection';
      case 1:
        return 'Data Selection';
      case 2:
        return 'Configuration';
      default:
        return 'Training';
    }
  }
}
