import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  computed,
  inject,
  Input,
} from '@angular/core';
import { FormGroup, ReactiveFormsModule } from '@angular/forms';
import { MatSelectChange, MatSelectModule } from '@angular/material/select';
import { TrainingStore } from '../../store/training.store';

@Component({
  selector: 'app-step-1',
  imports: [MatSelectModule, ReactiveFormsModule],
  templateUrl: './step-1.component.html',
  styleUrl: './step-1.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class Step1Component {
  @Input() trainingForm!: FormGroup;

  readonly store = inject(TrainingStore);
  constructor(private cdr: ChangeDetectorRef) {}
  getModelsByFilter(
    filterKey: 'category' | 'subcategory' | 'model',
    filterValue: MatSelectChange,
  ) {
    this.store.setSelectedModel(filterKey, filterValue.value);
    this.cdr.markForCheck();
  }

  categoryList = computed(() => ({
    categories: this.store.category(),
    subcategories: this.store.subcategory(),
    model: this.store.model().map(model => model.name),
  }));
}
