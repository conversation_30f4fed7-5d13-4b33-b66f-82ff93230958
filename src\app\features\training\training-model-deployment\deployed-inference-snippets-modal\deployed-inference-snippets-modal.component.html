<div class="ml-inference-new-deployment-modal h-full w-full bg-[#F1F3F9]">
  <div class="overflow-y-auto">
    <!-- HEADER -->
    <div
      class="flex items-center justify-between p-2 border-gray-200 dark:border-gray-700">
      <h2 mat-dialog-title>Inference Code Snippets</h2>
      <button
        mat-icon-button
        type="button"
        (click)="closeModal()"
        class="text-gray-400 hover:text-gray-600 p-2">
        <mat-icon>close</mat-icon>
      </button>
    </div>
    <hr />

    <div mat-dialog-content>
      <div
        class="api-inference-tab-container max-h-[250px] overflow-auto rounded-lg">
        <mat-tab-group>
          <mat-tab label="Predict">
            <app-multi-lang-snippet-container
              [snippets]="
                this.predictCodeSnippetForInference
              "></app-multi-lang-snippet-container>
          </mat-tab>
          <mat-tab label="Stop">
            <app-multi-lang-snippet-container
              [snippets]="
                this.stopCodeSnippetForInference
              "></app-multi-lang-snippet-container>
          </mat-tab>
          <mat-tab label="Status">
            <app-multi-lang-snippet-container
              [snippets]="
                this.statusCodeSnippetForInference
              "></app-multi-lang-snippet-container
          ></mat-tab>
        </mat-tab-group>
      </div>
    </div>

    <div mat-dialog-actions class="flex justify-end gap-2 mt-4">
      <button mat-flat-button (click)="closeModal()" type="button">
        Close
      </button>
    </div>
  </div>
</div>
