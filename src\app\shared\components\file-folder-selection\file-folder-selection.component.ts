import { FlatTreeControl } from '@angular/cdk/tree';
import {
  Component,
  ElementRef,
  EventEmitter,
  HostListener,
  inject,
  Input,
  OnDestroy,
  Output,
  ViewChild,
} from '@angular/core';
import { MatTreeModule } from '@angular/material/tree';
import { MatIconModule } from '@angular/material/icon';
import { CommonModule } from '@angular/common';
import {
  DynamicDatabase,
  DynamicDataSource,
  DynamicFlatNode,
} from './file-folder-selection.class';
import {
  MatCheckboxChange,
  MatCheckboxModule,
} from '@angular/material/checkbox';
import { Store } from '@ngrx/store';
import { resetFileState } from '../../../core/store/actions/file-folder.action';

@Component({
  selector: 'app-file-folder-selection',
  imports: [MatTreeModule, MatIconModule, CommonModule, MatCheckboxModule],
  templateUrl: './file-folder-selection.component.html',
  styleUrl: './file-folder-selection.component.css',
})
export class FileFolderSelectionComponent implements OnDestroy {
  treeControl!: FlatTreeControl<DynamicFlatNode>;
  isDropdownOpen = false;
  selectedNodes = new Set<string>();
  selectedLabel = '';
  selectedFileFolder: {
    name: string;
    id: number;
    path: string;
    type?: string;
    checked: boolean;
  } = {
    name: '',
    id: 0,
    path: '',
    checked: false,
  };
  @Output() selected = new EventEmitter<{
    name: string;
    id: number;
    path: string;
    type?: string;
    checked: boolean;
  }>();
  @Input() selectionType: string | undefined;
  dataSource!: DynamicDataSource;
  projectId = localStorage.getItem('project_id');
  @ViewChild('dropdownContainer', { static: false })
  dropdownContainer!: ElementRef;

  constructor(private store: Store) {
    const database = inject(DynamicDatabase);
    this.treeControl = new FlatTreeControl<DynamicFlatNode>(
      this.getLevel,
      this.isExpandable,
    );
    this.dataSource = new DynamicDataSource(this.treeControl, database);
    this.dataSource.data = database.initialData([]);
  }

  toggleDropdown() {
    this.isDropdownOpen = !this.isDropdownOpen;
  }

  // close the dropdown if click outside the dropdown-container
  @HostListener('document:click', ['$event'])
  onDocumentClick(event: MouseEvent) {
    const clickedInside = this.dropdownContainer.nativeElement.contains(
      event.target,
    );
    if (!clickedInside) {
      this.isDropdownOpen = false;
    }
  }

  hasChild = (_: number, _nodeData: DynamicFlatNode) => _nodeData.expandable;

  getFileIcon(name: string): string {
    if (name.endsWith('.csv')) return 'table_chart';
    if (name.endsWith('.jpg') || name.endsWith('.png')) return 'image';
    return 'insert_drive_file';
  }
  getLevel = (node: DynamicFlatNode) => node.level;

  isExpandable = (node: DynamicFlatNode) => node.expandable;

  isSelected(node: DynamicFlatNode, event: MatCheckboxChange) {
    this.selectedFileFolder = {
      name: node.item.name,
      id: node.item.id,
      path: node.item.path,
      type: node.item.type,
      checked: event.checked,
    };
    this.selectedLabel = event.checked
      ? node.item.type === 'file'
        ? '1 file selected'
        : '1 folder selected'
      : '';
    this.selected.emit(this.selectedFileFolder);
  }
  isNodeChecked(node: DynamicFlatNode): boolean {
    return (
      node.item.name === this.selectedFileFolder.name &&
      this.selectedFileFolder.checked
    );
  }

  ngOnDestroy(): void {
    this.store.dispatch(resetFileState());
  }
}
