<app-loader [loading]="loading"></app-loader>
<link
  href="https://cdnjs.cloudflare.com/ajax/libs/intro.js/7.2.0/introjs.min.css"
  rel="stylesheet" />
<div class="flex flex-row justify-between w-full h-26 px-6">
  <h2 class="dark:text-white">{{ g_const.projects }}</h2>
  <div class="flex items-center space-x-4">
    <div>
      <app-search-header
        (searchPerformed)="onSearchPerformed($event)"></app-search-header>
    </div>
    <div>
      <button id="step0" mat-flat-button (click)="openAddForm()">
        <mat-icon>add</mat-icon>
        <span class="hidden sm:block">New {{ g_const.project }} </span>
      </button>
    </div>
  </div>
</div>

<div
  class="flex-grow mt-4 overflow-y-auto scrollbar-hidden scroll-container h-[85vh]"
  #scrollContainer>
  <div class="grid grid-cols-12 gap-7 p-4 mt-8 mb-8" id="step1-1">
    <mat-card
      class="rounded-xl h-52 col-span-12 md:col-span-6 xl:col-span-4 p-2"
      *ngFor="let project of paginatedProjects">
      <div
        class="flex items-center justify-between w-full h-12 border-b border-gray-300 p-2">
        <button
          mat-icon-button
          [matMenuTriggerFor]="menu"
          aria-label="Example icon-button with a menu">
          <mat-icon>more_vert</mat-icon>
        </button>
        <mat-menu #menu="matMenu" class="w-[335px] h-28">
          <div class="h-[72px]">
            <button mat-menu-item class="h-9" (click)="openEditForm(project)">
              <mat-icon>edit</mat-icon>
              <span>Edit {{ g_const.project }}</span>
            </button>
            <button
              mat-menu-item
              class="h-9"
              (click)="confirmDelete(project.id, project.title)">
              <mat-icon>delete_outline</mat-icon>
              <span>Delete {{ g_const.project }}</span>
            </button>
          </div>
        </mat-menu>
        <div class="flex items-center flex-grow">
          <mat-card-title
            class="whitespace-nowrap overflow-hidden text-ellipsis max-w-[355px]">
            {{ getProjectTitle(project.title) }}
          </mat-card-title>
        </div>
        <button
          id="step1-2"
          mat-icon-button
          (click)="selectProject(project.id)">
          <mat-icon>chevron_right</mat-icon>
        </button>
      </div>
      <mat-card-content class="flex-grow mt-4 overflow-hidden">
        <p class="text-sm font-normal text-gray-600">
          {{ project.description }}
        </p>
      </mat-card-content>
      <mat-card-actions
        class="flex justify-between p-4 text-sm text-gray-500 font-medium">
        <span class="text-sm"
          >Files <strong class="text-sm">{{ project.file_count }}</strong></span
        >
        <span class="text-sm"
          >Data Size:
          <strong class="text-sm">{{ project.files_size }}</strong></span
        >
      </mat-card-actions>
    </mat-card>
  </div>
  <div *ngIf="loading" class="text-center"></div>

  <div
    *ngIf="projects.length === 0"
    class="hidden sm:block flex flex-col items-center justify-center rounded-xl h-52 col-span-12 md:col-span-6 xl:col-span-4 p-2">
    <mat-card
      class="w-full max-w-1/2 max-h-2/3 flex flex-col items-center justify-center p-6 rounded-lg shadow-lg">
      <div
        class="relative cursor-pointer"
        (click)="openAddForm()"
        (keyup.enter)="openAddForm()"
        tabindex="0">
        <img
          src="assets/project/Polygon1.svg"
          alt="Cute illustration"
          class="w-32 h-32 mb-4" />
        <img
          src="assets/project/Frame2156.svg"
          alt="Folder illustration"
          class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-30 h-30" />
      </div>
      <p class="text-lg font-medium text-gray-600 mb-1">
        Create your first project and start innovating.
      </p>
      <p class="text-sm font-light text-gray-400">
        It’s easy to get started — just click the icon.
      </p>
    </mat-card>
  </div>
</div>
<app-project-modal
  *ngIf="projectPopup"
  [formData]="formData"
  [entityName]="'Project'"
  [labelName]="'Project Name'"
  [labelDescription]="'Description'"
  [placeholderTitle]="'Project Name'"
  [placeholderDescription]="'Write a description for your project...'"
  [modelType]="'project'"
  (saveProjectEvent)="saveProject($event)"
  (cancelEvent)="closePopup()"></app-project-modal>

<app-delete-modal
  *ngIf="projectDeletePopup"
  (deleteEvent)="deleteProject()"
  (cancelEvent)="closePopup()">
</app-delete-modal>
