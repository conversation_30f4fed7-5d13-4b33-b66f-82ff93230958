<div class="h-full w-full">
  <div class="flex flex-col h-full w-full">
    <!-- section 1 -->
    <div class="grid grid-cols-[70%_30%] p-5">
      <!-- Description info -->
      <div>
        <div class="flex flex-row items-center gap-1">
          <h3>AI Model Deployment</h3>
          <button
            mat-icon-button
            (click)="openInfoModal()"
            class="flex justify-center items-center">
            <mat-icon>info_outline</mat-icon>
          </button>
        </div>
      </div>
      <!-- New Deployment button -->
      <div class="flex justify-end items-center">
        <button
          mat-flat-button
          color="primary"
          (click)="openNewDeploymentModal()">
          New Deployment
        </button>
      </div>
    </div>

    <!-- section 2 -->
    <div class="flex-1 api-section overflow-auto">
      <app-tab-container></app-tab-container>
    </div>
  </div>
</div>
