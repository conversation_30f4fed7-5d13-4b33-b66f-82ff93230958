import { BrowserModule } from '@angular/platform-browser';
import { NgModule } from '@angular/core';
import { HttpClientModule } from '@angular/common/http';
import { SharedModule } from './shared/shared.module';
import { ToastrModule } from 'ngx-toastr';
import { AppRoutingModule } from './app-routing.module';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { RouterModule } from '@angular/router';
import { routes } from './app.routes';
import { NgxStripeModule } from 'ngx-stripe';
import { PlotlyViaCDNModule } from 'angular-plotly.js';
import { environment } from './env/env';
import { IntroService } from './introjs.service';
import { EffectsModule } from '@ngrx/effects';
import { FileFolderEffects } from './core/store/effects/file-folder.effects';
@NgModule({
  declarations: [],
  imports: [
    BrowserModule,
    AppRoutingModule,
    RouterModule.forRoot(routes),
    HttpClientModule,
    NgxStripeModule.forRoot(environment.stripe.publicKey),
    SharedModule,
    MatDatepickerModule,
    MatNativeDateModule,
    BrowserAnimationsModule,
    ToastrModule.forRoot({
      timeOut: 10000,
      positionClass: 'toast-top-center',
      preventDuplicates: true,
    }),
    PlotlyViaCDNModule,
    EffectsModule.forRoot([FileFolderEffects]),
  ],
  providers: [IntroService],
  bootstrap: [],
})
export class AppModule {}
