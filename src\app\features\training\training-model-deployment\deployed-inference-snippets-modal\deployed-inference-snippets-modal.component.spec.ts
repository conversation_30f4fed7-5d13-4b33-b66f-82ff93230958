import { ComponentFixture, TestBed } from '@angular/core/testing';

import { DeployedInferenceSnippetsModalComponent } from './deployed-inference-snippets-modal.component';

describe('DeployedInferenceSnippetsModalComponent', () => {
  let component: DeployedInferenceSnippetsModalComponent;
  let fixture: ComponentFixture<DeployedInferenceSnippetsModalComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [DeployedInferenceSnippetsModalComponent],
    }).compileComponents();

    fixture = TestBed.createComponent(DeployedInferenceSnippetsModalComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
