import { Component } from '@angular/core';
import { MatTabsModule } from '@angular/material/tabs';
import { OnInit } from '@angular/core';
import { ApiInferenceService } from '../../service/api-inference.service';
import { TrainingService } from '../../service/training-service.service';
import { CodeSnippetMeta } from '../../models/api-inference-code-snippet.model';
import { MultiLangSnippetContainerComponent } from '../multi-lang-snippet-container/multi-lang-snippet-container.component';

@Component({
  selector: 'app-api-quickstart-container',
  imports: [MatTabsModule, MultiLangSnippetContainerComponent],
  templateUrl: './api-quickstart-container.component.html',
  styleUrl: './api-quickstart-container.component.css',
})
export class ApiQuickstartContainerComponent implements OnInit {
  constructor(
    private apiInferenceService: ApiInferenceService,
    private trainingService: TrainingService,
  ) {}

  uiData: CodeSnippetMeta[] = [];

  ngOnInit() {
    const training_id = this.trainingService.getTrainingIdFromUrl();
    if (training_id) {
      this.apiInferenceService.getQuickStartCodeSnippet(training_id).then(
        response => {
          this.uiData = response.data?.results || [];
        },
        error => {
          console.error('Error fetching quick start code snippet:', error);
          this.uiData = [];
        },
      );
    }
  }
}
