# Dark Mode Architecture Documentation

## Overview

The dark mode implementation uses a **hybrid theming approach** that combines Angular Material's theming API with custom CSS class-based theming. This provides automatic theming for Material components while maintaining full control over custom components.

## Architecture Components

### 1. Service Initialization

**Location**: `src/app/core/services/theme.service.ts`

The `ThemeService` is initialized through Angular's dependency injection system via `app.config.ts`:

```typescript
// app.config.ts
{
  provide: 'THEME_INITIALIZER',
  useFactory: (_themeService: ThemeService) => {
    // ThemeService is injected for constructor side effects (initialization)
    return () => Promise.resolve();
  },
  deps: [ThemeService],
}
```

**What happens in the service constructor:**
1. **Theme Detection**: Checks localStorage for saved preference, falls back to system preference
2. **Store Initialization**: Dispatches initial theme state to NgRx store
3. **DOM Subscription**: Subscribes to theme changes and applies CSS classes to `<body>`
4. **Persistence**: Automatically saves theme changes to localStorage

### 2. State Management Flow

**NgRx Store Structure:**
- **State**: `ThemeState` with `themeMode: 'light' | 'dark'`
- **Actions**: `toggleTheme()`, `setTheme({ themeMode })`
- **Reducer**: Handles theme state transitions
- **Selector**: `selectThemeMode` for reactive subscriptions

**Flow:**
```
User Click → ThemeService.toggleTheme() → NgRx Action → Reducer → Store Update → 
Selector Emission → Service Subscription → DOM Class Update → CSS Application
```

### 3. CSS Theming Architecture (Hybrid Approach)

#### A. Angular Material Components (Automatic)
**Location**: `styles.scss`

```scss
// Light theme (default)
:root {
  @include mat.all-component-themes($theme);
}

// Dark theme
.dark-mode {
  @include mat.all-component-colors($dark-theme);
}
```

**Benefits:**
- All Material components (mat-button, mat-card, etc.) get automatic theming
- Consistent Material Design aesthetics with proper hover/focus states
- No manual styling needed for Material components

#### B. Custom Components (Manual)
**Location**: `src/styles/_color.scss` + `src/styles/_utilities.scss`

```scss
// CSS Custom Properties that change based on body class
.light-mode {
  --md-sys-color-surface: #f7f9ff;
  --md-sys-color-on-surface: #1d1b20;
}

.dark-mode {
  --md-sys-color-surface: #111418;
  --md-sys-color-on-surface: #e1e2e8;
}

// Utility classes using the variables
.bg-card {
  background-color: var(--md-sys-color-surface);
}
```

### 4. Component Theme Updates

#### Method 1: Observable Subscription (Reactive)
For components that need to react to theme changes:

```typescript
export class MyComponent {
  currentTheme$: Observable<ThemeMode>;

  constructor(private themeService: ThemeService) {
    this.currentTheme$ = this.themeService.currentTheme$;
  }
}
```

```html
@if (currentTheme$ | async; as theme) {
  @if (theme === 'dark') {
    <!-- Dark mode specific content -->
  }
}
```

#### Method 2: CSS Classes (Automatic)
For styling that should automatically switch:

```scss
// Component styles automatically switch based on body class
:host-context(.dark-mode) .my-element {
  background-color: #181c20;
}

// Or use utility classes
<div class="bg-card text-on-surface">Content</div>
```

#### Method 3: Tailwind Dark Mode
**Configuration**: `tailwind.config.js`

```javascript
module.exports = {
  darkMode: ['class', '.dark-mode'], // Watch for .dark-mode class
};
```

```html
<div class="bg-white dark:bg-gray-800">
  Content that switches automatically
</div>
```

## Implementation Patterns

### For New Components

1. **Use utility classes** from `_utilities.scss` for consistent theming
2. **Subscribe to theme observable** only if you need conditional logic
3. **Use Tailwind dark:** classes for simple color switches
4. **Use :host-context(.dark-mode)** for component-specific styling

### For Existing Components

1. **Material components**: No changes needed (automatic theming)
2. **Custom elements**: Add utility classes or dark mode CSS
3. **Third-party components**: Use :host-context(.dark-mode) overrides

## File Structure

```
src/
├── app/
│   ├── core/
│   │   ├── services/
│   │   │   └── theme.service.ts          # Main theme service
│   │   └── store/
│   │       ├── actions/theme.action.ts   # NgRx actions
│   │       ├── reducers/theme.reducer.ts # NgRx reducer
│   │       ├── selectors/theme.reducer.ts # NgRx selectors
│   │       └── states/theme.state.ts     # Type definitions
│   └── shared/
│       └── components/
│           └── theme-toggle/             # Toggle button component
├── styles/
│   ├── _color.scss                       # CSS custom properties
│   └── _utilities.scss                   # Theme-aware utility classes
├── styles.scss                           # Angular Material theming
├── m3-theme.scss                         # Material 3 theme definitions
└── tailwind.config.js                    # Tailwind dark mode config
```

## Key Design Decisions

### Why Hybrid Approach?
1. **Angular Material**: Provides consistent, accessible theming for all Material components
2. **Custom CSS**: Gives complete control over custom components and third-party libraries
3. **Best of Both**: Combines automatic theming with flexibility

### Why NgRx Store?
1. **Reactive Updates**: All components automatically receive theme changes
2. **Centralized State**: Single source of truth for theme state
3. **Persistence**: Easy integration with localStorage
4. **Debugging**: Redux DevTools support for theme state

### Why Body Classes?
1. **Global Scope**: CSS classes on body affect entire application
2. **CSS Cascade**: Natural CSS inheritance and specificity
3. **Framework Agnostic**: Works with any CSS framework or custom styles
4. **Performance**: No JavaScript needed for most styling updates

## Developer Handover Checklist

### To Add Dark Mode to a New Component:

1. **Check if it uses Material components**: If yes, no action needed
2. **For custom styling**: Use utility classes from `_utilities.scss`
3. **For conditional logic**: Subscribe to `themeService.currentTheme$`
4. **For Tailwind**: Use `dark:` prefix classes
5. **For complex styling**: Add `:host-context(.dark-mode)` rules

### To Debug Theme Issues:

1. **Check Redux DevTools**: Verify theme state changes
2. **Inspect body classes**: Ensure `.light-mode` or `.dark-mode` is applied
3. **Check CSS specificity**: Dark mode styles might be overridden
4. **Verify CSS custom properties**: Use browser dev tools to check variable values

### Common Patterns:

```typescript
// Pattern 1: Reactive theme subscription
currentTheme$ = this.themeService.currentTheme$;

// Pattern 2: One-time theme check
const currentTheme = await firstValueFrom(this.themeService.currentTheme$);

// Pattern 3: Theme toggle
this.themeService.toggleTheme();

// Pattern 4: Set specific theme
this.themeService.setTheme('dark');
```

## Testing Considerations

- **Unit Tests**: Mock `ThemeService` and test component behavior with different themes
- **E2E Tests**: Test theme toggle functionality and visual consistency
- **Accessibility**: Verify color contrast ratios in both themes
- **Performance**: Ensure theme switching is smooth without layout shifts
