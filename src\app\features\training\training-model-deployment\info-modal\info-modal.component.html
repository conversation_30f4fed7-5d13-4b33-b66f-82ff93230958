<div class="ml-inference-new-deployment-modal h-full w-full bg-[#F1F3F9]">
  <div class="overflow-y-auto">
    <!-- HEADER -->
    <div
      class="flex items-center justify-between p-2 border-gray-200 dark:border-gray-700">
      <h2 mat-dialog-title>API Model Deployment</h2>
      <button
        mat-icon-button
        type="button"
        (click)="closeModal()"
        class="text-gray-400 hover:text-gray-600">
        <mat-icon>close</mat-icon>
      </button>
    </div>
    <hr />

    <div mat-dialog-content>
      <div>
        <p>
          Deploy your trained model on dedicated servers provisioned exclusively
          for your use and are configured with aicuflow inference out of the box
          through APIs. There are two server types: CPU and GPU.
          <br />
          There are three environments for deployment: Development, Production
          and On-Demand.
        </p>
        <br />
        <br />
        <p>
          Development Environments last 1- 8 h and are ideal for integration
          testing, prototyping and short-term experiment.
          <br />
          Production Environments run until they are excplicitly terminated and
          are ideal for long-running applications, production workloads.
          <br />
          On-Demand Environments are up when used and after 5-10 minutes of
          inactivity then shut down. In case of another API request they are
          active again. This is ideal for tasks where users can wait for a
          result.
        </p>
        <br />
        <br />
        <p>
          Contact support if you need Production or Development environment.
        </p>
      </div>
    </div>

    <div mat-dialog-actions class="flex justify-end gap-2 mt-4">
      <button mat-flat-button type="submit" (click)="closeModal()">
        close
      </button>
    </div>
  </div>
</div>
