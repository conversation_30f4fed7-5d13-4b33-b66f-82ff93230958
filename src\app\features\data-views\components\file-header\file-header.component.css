/* Dark mode styling for file-header buttons */
:host-context(.dark-mode) .stroke-btn {
  background-color: #181c20 !important;
  border-color: #43474e !important;
  color: var(--md-sys-color-on-surface) !important;
}

:host-context(.dark-mode) .stroke-btn:hover {
  background-color: #1f2328 !important;
}

/* Dark mode styling for button toggle groups */
:host-context(.dark-mode) mat-button-toggle-group {
  background-color: #181c20 !important;
  border-color: #43474e !important;
}

:host-context(.dark-mode) mat-button-toggle {
  background-color: #181c20 !important;
  color: var(--md-sys-color-on-surface) !important;
  border-color: #43474e !important;
}

:host-context(.dark-mode) mat-button-toggle:hover {
  background-color: #1f2328 !important;
}

/* Dark mode styling for checked/selected button toggles */
:host-context(.dark-mode) mat-button-toggle.mat-button-toggle-checked {
  background-color: var(--md-sys-color-primary) !important;
  color: var(--md-sys-color-on-primary) !important;
}

/* Dark mode styling for back button */
:host-context(.dark-mode) button[mat-icon-button] {
  color: var(--md-sys-color-primary) !important;
}
