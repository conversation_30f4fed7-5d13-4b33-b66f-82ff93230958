import { Component, inject, OnInit } from '@angular/core';
import { TrainingAppStore } from '../../store/training-app.store';
import { InferenceInstanceCardComponent } from '../inference-instance-card/inference-instance-card.component';
import { EmptyStateCardComponent } from '../../../../shared/components/empty-state-card/empty-state-card.component';

@Component({
  selector: 'app-deployed-environments-container',
  imports: [InferenceInstanceCardComponent, EmptyStateCardComponent],
  templateUrl: './deployed-environments-container.component.html',
  styleUrl: './deployed-environments-container.component.css',
})
export class DeployedEnvironmentsContainerComponent implements OnInit {
  trainingAppStore = inject(TrainingAppStore);

  ngOnInit(): void {
    this.getDlInferenceJobsInStore();
  }

  getDlInferenceJobsInStore() {
    try {
      const trainingId = this.trainingAppStore.trainingInfo().id;
      if (!trainingId) {
        throw Error('Unable to fetch trainingID from URL');
      }
      this.trainingAppStore.getDlInferenceJobs(trainingId);
    } catch (err) {
      console.error('Error while getting inference jobs', err);
    }
  }
}
