<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta
      http-equiv="Content-Security-Policy"
      content="
      script-src 'self' https://cdn.plot.ly https://js.stripe.com https://eu.i.posthog.com https://eu-assets.i.posthog.com/ 'unsafe-inline' 'unsafe-eval';
      script-src-elem 'self' https://cdn.plot.ly https://js.stripe.com https://eu.i.posthog.com https://eu-assets.i.posthog.com/ 'unsafe-inline' 'unsafe-eval';
    " />

    <!-- HTML/ Primary Meta Tags -->
    <title>aicuflow - Research and Development with Explainable AI</title>
    <meta
      name="title"
      content="aicuflow - Research and Development with Explainable AI" />
    <meta
      name="description"
      content="aicuflow transforms research with explainable AI. Its multimodal tool automates workflows, cuts R&D cycles by 50%, and seamlessly grows with project stages while integrating with existing processes." />

    <!-- Open Graph / Facebook Meta Tags -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://platform.aicuflow.com/" />
    <meta
      property="og:title"
      content="aicuflow - Research and Development with Explainable AI" />
    <meta
      property="og:description"
      content="aicuflow transforms research with explainable AI. Its multimodal tool automates workflows, cuts R&D cycles by 50%, and seamlessly grows with project stages while integrating with existing processes." />
    <meta
      property="og:image"
      content="https://platform.aicuflow.com/assets/images/AICULogoDesign_square.png" />
    <meta property="og:image:height" content="630" />
    <meta property="og:image:width" content="1200" />
    <meta name="type" property="og:type" content="website" />

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:domain" content="platform.aicuflow.com" />
    <meta property="twitter:url" content="https://platform.aicuflow.com/" />
    <meta
      property="twitter:title"
      content="aicuflow - Research and Development with Explainable AI" />
    <meta
      property="twitter:description"
      content="aicuflow transforms research with explainable AI. Its multimodal tool automates workflows, cuts R&D cycles by 50%, and seamlessly grows with project stages while integrating with existing processes." />
    <meta
      property="twitter:image"
      content="https://platform.aicuflow.com/assets/images/AICULogoDesign_square.png" />

    <script type="application/ld+json">
      {
        "@context": "https://schema.org",
        "@type": "Article",
        "name": "aicuflow",
        "description": "aicuflow transforms research with explainable AI. Its multimodal tool automates workflows, cuts R&D cycles by 50%, and seamlessly grows with project stages while integrating with existing processes."
      }
    </script>

    <base href="/" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <link rel="icon" type="image/x-icon" href="./assets/Logo.png" />
    <link
      href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500&display=swap"
      rel="stylesheet" />
    <link
      href="https://fonts.googleapis.com/icon?family=Material+Icons"
      rel="stylesheet" />
    <link
      href="https://fonts.googleapis.com/icon?family=Material+Icons+Outlined"
      rel="stylesheet" />

    <!-- Apple Touch Icon for iOS devices -->
    <link
      rel="apple-touch-icon"
      href="https://platform.aicuflow.com/assets/images/apple_touch_icons/apple-touch-icon-iphone-60x60.png" />
    <link
      rel="apple-touch-icon"
      sizes="60x60"
      href="https://platform.aicuflow.com/assets/images/apple_touch_icons/apple-touch-icon-iphone-60x60.png" />
    <link
      rel="apple-touch-icon"
      sizes="72x72"
      href="https://platform.aicuflow.com/assets/images/apple_touch_icons/apple-icon-72x72.png" />
    <link
      rel="apple-touch-icon"
      sizes="76x76"
      href="https://platform.aicuflow.com/assets/images/apple_touch_icons/apple-touch-icon-ipad-76x76.png" />
    <link
      rel="apple-touch-icon"
      sizes="114x114"
      href="https://platform.aicuflow.com/assets/images/apple_touch_icons/apple-icon-114x114.png" />
    <link
      rel="apple-touch-icon"
      sizes="120x120"
      href="https://platform.aicuflow.com/assets/images/apple_touch_icons/apple-touch-icon-iphone-retina-120x120.png" />
    <link
      rel="apple-touch-icon"
      sizes="144x144"
      href="https://platform.aicuflow.com/assets/images/apple_touch_icons/apple-icon-144x144.png" />
    <link
      rel="apple-touch-icon"
      sizes="152x152"
      href="https://platform.aicuflow.com/assets/images/apple_touch_icons/apple-touch-icon-ipad-retina-152x152.png" />
    <link
      rel="apple-touch-icon"
      sizes="180x180"
      href="https://platform.aicuflow.com/assets/images/apple_touch_icons/apple-icon-180x180.png" />

    <!-- Favicon for standard browsers -->
    <link
      rel="icon"
      type="image/png"
      sizes="152x152"
      href="https://platform.aicuflow.com/assets/images/apple_touch_icons/android-chrome-512x512.png" />

    <link
      rel="manifest"
      href="https://platform.aicuflow.com/assets/images/apple_touch_icons/manifest.json" />

    <link
      rel="icon"
      type="image/png"
      sizes="192x192"
      href="https://platform.aicuflow.com/assets/images/apple_touch_icons/android-icon-192x192.png" />
    <link
      rel="icon"
      type="image/png"
      sizes="48x48"
      href="https://platform.aicuflow.com/assets/images/apple_touch_icons/android-icon-48x48.png" />
    <link
      rel="icon"
      type="image/png"
      sizes="96x96"
      href="https://platform.aicuflow.com/assets/images/apple_touch_icons/favicon-96x96.png" />
    <link
      rel="icon"
      type="image/png"
      sizes="32x32"
      href="https://platform.aicuflow.com/assets/images/apple_touch_icons/favicon-32x32.png" />
    <link
      rel="icon"
      type="image/png"
      sizes="16x16"
      href="https://platform.aicuflow.com/assets/images/apple_touch_icons/favicon-16x16.png" />
    <!-- ICO format for legacy browsers -->
    <link
      rel="icon"
      type="image/x-icon"
      href="https://platform.aicuflow.com/favicon.ico" />

    <meta name="msapplication-TileColor" content="#ffffff" />
    <meta
      name="msapplication-TileImage"
      content="https://platform.aicuflow.com/assets/images/apple_touch_icons/ms-icon-144x144.png" />
    <meta name="theme-color" content="#ffffff" />

    <script>
      !(function (t, e) {
        var o, n, p, r;
        e.__SV ||
          ((window.posthog = e),
          (e._i = []),
          (e.init = function (i, s, a) {
            function g(t, e) {
              var o = e.split('.');
              (2 == o.length && ((t = t[o[0]]), (e = o[1])),
                (t[e] = function () {
                  t.push([e].concat(Array.prototype.slice.call(arguments, 0)));
                }));
            }
            (((p = t.createElement('script')).type = 'text/javascript'),
              (p.async = !0),
              (p.src =
                s.api_host.replace('.i.posthog.com', '-assets.i.posthog.com') +
                '/static/array.js'),
              (r = t.getElementsByTagName('script')[0]).parentNode.insertBefore(
                p,
                r,
              ));
            var u = e;
            for (
              void 0 !== a ? (u = e[a] = []) : (a = 'posthog'),
                u.people = u.people || [],
                u.toString = function (t) {
                  var e = 'posthog';
                  return (
                    'posthog' !== a && (e += '.' + a),
                    t || (e += ' (stub)'),
                    e
                  );
                },
                u.people.toString = function () {
                  return u.toString(1) + '.people (stub)';
                },
                o =
                  'capture identify alias people.set people.set_once set_config register register_once unregister opt_out_capturing has_opted_out_capturing opt_in_capturing reset isFeatureEnabled onFeatureFlags getFeatureFlag getFeatureFlagPayload reloadFeatureFlags group updateEarlyAccessFeatureEnrollment getEarlyAccessFeatures getActiveMatchingSurveys getSurveys getNextSurveyStep onSessionId'.split(
                    ' ',
                  ),
                n = 0;
              n < o.length;
              n++
            )
              g(u, o[n]);
            e._i.push([i, s, a]);
          }),
          (e.__SV = 1));
      })(document, window.posthog || []);
      posthog.init('phc_4Re7ZGWFRQW3WCa8GE0mVcyLgchWNt4jykob86HOQXg', {
        api_host: 'https://eu.i.posthog.com',
        person_profiles: 'identified_only', // or 'always' to create profiles for anonymous users as well
      });
    </script>
  </head>
  <body class="mat-typography mat-app-background">
    <app-root></app-root>
  </body>
</html>
