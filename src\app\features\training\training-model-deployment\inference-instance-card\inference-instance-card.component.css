.status-box {
  position: relative;
  background-color: #f0fdf4; /* light neutral background */
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  overflow: hidden;
  min-width: 100px;
  text-align: center;
}

.status-text {
  position: relative;
  z-index: 1;
}

/* Shared breath animation */
.breath-overlay {
  position: absolute;
  inset: 0;
  opacity: 0.5;
  animation: breath-pulse 2s ease-in-out infinite;
}

/* Variants */
.breath-green {
  background-color: #4ade80; /* green-400 */
}

.breath-red {
  background-color: #f87171; /* red-400 */
}

.breath-yellow {
  background-color: #facc15; /* yellow-400 */
}

@keyframes breath-pulse {
  0%,
  100% {
    opacity: 0.3;
  }
  50% {
    opacity: 0.6;
  }
}
