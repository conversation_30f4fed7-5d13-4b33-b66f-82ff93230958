import { ComponentFixture, TestBed } from '@angular/core/testing';

import { MultiLangSnippetContainerComponent } from './multi-lang-snippet-container.component';

describe('MultiLangSnippetContainerComponent', () => {
  let component: MultiLangSnippetContainerComponent;
  let fixture: ComponentFixture<MultiLangSnippetContainerComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [MultiLangSnippetContainerComponent],
    }).compileComponents();

    fixture = TestBed.createComponent(MultiLangSnippetContainerComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
