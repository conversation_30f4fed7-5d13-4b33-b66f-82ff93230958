<!-- <p *ngIf="loading" class="text-3xl text-red-700">Loading</p> -->
<app-loader [loading]="loading"></app-loader>

<div *ngIf="!loading">
  <div class="flex flex-row justify-between w-full h-26 px-6">
    <div class="flex flex-col dark:text-white" style="max-width: 450px">
      <span
        #titleElement
        class="ellipsis"
        [matTooltip]="projectData?.data?.title"
        matTooltipPosition="above"
        [matTooltipDisabled]="!showTooltip"
        >{{ projectData?.data?.title }}</span
      >
      <h2>{{ g_const.overview }}</h2>
    </div>
    <div class="flex items-center space-x-4">
      <div>
        <app-search-header
          (searchPerformed)="onSearchPerformed($event)"
          [filter]="false"></app-search-header>
      </div>
    </div>
  </div>
  <div id="step2-1" class="scrollbar-hidden overflow-y-auto h-[85vh]">
    <div class="grid grid-cols-12 gap-4 gap-7 p-4 mt-8">
      <mat-card class="rounded-xl h-[300px] col-span-12 md:col-span-4">
        <mat-card-header
          class="border-b border-gray-300 flex justify-start pt-0">
          <mat-card-title class="text-gray-800 text-center w-full py-3">
            {{ g_const.description }}
          </mat-card-title>
        </mat-card-header>
        <mat-card-content class="content-scrollable">
          <p class="text-gray-600 pt-2">
            {{ projectData?.data?.description }}
          </p>
        </mat-card-content>
      </mat-card>
      <div class="col-span-12 md:col-span-8">
        <mat-card class="rounded-xl h-[300px] flex-1 flex flex-col">
          <mat-card-header
            class="border-b border-gray-300 flex justify-start pt-0">
            <mat-card-title class="text-gray-800 text-center w-full py-3">
              Hypotheses
            </mat-card-title>
            <button
              class="ml-auto mt-2"
              mat-icon-button
              (click)="openAddForm()">
              <mat-icon>add</mat-icon>
            </button>
          </mat-card-header>
          <mat-card-content class="content-scrollable pt-2">
            <!-- No data found message -->
            <div
              *ngIf="hypothesis_data.length === 0"
              class="text-center text-gray-500">
              No Hypotheses data found.
            </div>

            <!-- Hypothesis data display -->
            <div *ngIf="hypothesis_data.length > 0">
              <div *ngFor="let hypothesis of hypothesis_data">
                <div class="flex items-center dark:text-white">
                  <button
                    class="material-icons-outlined rounded-full mr-4 w-10 h-10 p-2 gap-2"
                    [matMenuTriggerFor]="menu">
                    more_vert
                  </button>
                  <mat-menu #menu="matMenu" class="w-[335px] h-28">
                    <div class="h-[72px]">
                      <button
                        mat-menu-item
                        class="h-9"
                        (click)="openEditForm(hypothesis)">
                        <mat-icon>edit</mat-icon>
                        <span>Edit Hypothesis</span>
                      </button>
                      <button
                        mat-menu-item
                        class="h-9"
                        (click)="confirmDelete(hypothesis.id!)">
                        <mat-icon>delete_outline</mat-icon>
                        <span>Delete Hypothesis</span>
                      </button>
                    </div>
                  </mat-menu>
                  <div>
                    <p class="font-sans font-medium !mb-1">
                      {{ hypothesis.hypothesis_type }}
                    </p>
                    <p class="text-gray-600">{{ hypothesis.hypothesis }}</p>
                  </div>
                </div>
              </div>
            </div>
          </mat-card-content>
        </mat-card>
      </div>
    </div>

    <div class="p-4 mt-8 mb-8">
      <div class="grid grid-cols-12 gap-7">
        <mat-card
          class="rounded-xl h-52 col-span-12 md:col-span-6 xl:col-span-4 p-2">
          <mat-card-header
            class="border-b border-gray-300 flex justify-start pt-1">
            <div class="flex items-center">
              <span
                class="material-icons-outlined text-[24px] text-gray-600 mr-2">
                difference
              </span>
              <mat-card-title class="text-gray-800 text-center w-full py-3">
                Data View
              </mat-card-title>
            </div>
            <button
              id="step2"
              class="ml-auto mt-1"
              mat-icon-button
              [routerLink]="['/project', project_id, 'data-view-v2']">
              <mat-icon>chevron_right</mat-icon>
            </button>
          </mat-card-header>
          <mat-card-content class="pt-4">
            <p class="text-gray-600 border-b border-gray-300 pb-2">
              {{ projectData?.data?.file_count }} files
            </p>
          </mat-card-content>
          <mat-card-content class="pt-3">
            <p class="text-gray-600 whitespace-nowrap">
              {{ projectData?.data?.files_size }} Data
            </p>
          </mat-card-content>
        </mat-card>

        <mat-card
          class="rounded-xl h-52 col-span-12 md:col-span-6 xl:col-span-4 p-2">
          <mat-card-header
            class="border-b border-gray-300 flex justify-start w-auto pt-1">
            <div class="flex items-center">
              <span
                class="material-icons-outlined text-[24px] text-gray-600 mr-2">
                insert_chart
              </span>
              <mat-card-title
                class="text-base text-gray-800 text-center w-full py-3 overflow-hidden whitespace-nowrap">
                Visual Data Insights
              </mat-card-title>
            </div>
            <button
              id="step2-5"
              class="ml-auto mt-1"
              mat-icon-button
              [routerLink]="['/project', project_id, 'data-insights']">
              <mat-icon>chevron_right</mat-icon>
            </button>
          </mat-card-header>
          <mat-card-content class="pt-4 pb-[57px] custom-card">
            <p class="text-gray-600">
              {{ projectData?.data?.plot_count }} plots
            </p>
          </mat-card-content>
        </mat-card>

        <!-- Hiding Training card for V1 -->

        <mat-card
          class="rounded-xl h-52 col-span-12 md:col-span-12 xl:col-span-4 p-2">
          <mat-card-header
            class="border-b border-gray-300 flex justify-start pt-1">
            <div class="flex items-center">
              <span
                class="material-icons-outlined text-[24px] text-gray-600 mr-2">
                model_training
              </span>
              <mat-card-title class="text-gray-800 text-center w-full py-3">
                Trainings
              </mat-card-title>
            </div>
            <button
              class="ml-auto mt-1"
              mat-icon-button
              [routerLink]="['/project', project_id, 'training']">
              <mat-icon>chevron_right</mat-icon>
            </button>
          </mat-card-header>
          <mat-card-content class="!pt-4">
            <p class="text-gray-600 border-b border-gray-300 pb-2">
              {{ projectData?.data?.train_runs }} train run
            </p>
          </mat-card-content>
          <mat-card-content class="pt-3">
            <p class="text-gray-600">
              {{ projectData?.data?.model_count }} training models
            </p>
          </mat-card-content>
        </mat-card>
      </div>
    </div>
  </div>
</div>

<app-project-modal
  *ngIf="hypothesisPopup"
  [formData]="formData"
  [entityName]="'Hypothesis'"
  [labelName]="'Hypothesis Type'"
  [labelDescription]="'Hypothesis'"
  [placeholderTitle]="'Hypothesis Type'"
  [placeholderDescription]="'Write a description for your hypothesis...'"
  [modelType]="'hypothesis'"
  (saveHypothesisEvent)="saveHypothesis($event)"
  (cancelEvent)="closePopup()"></app-project-modal>

<app-delete-modal
  *ngIf="hypothesisDeletePopup"
  (deleteEvent)="deleteProject(id.toString())"
  (cancelEvent)="closePopup()">
</app-delete-modal>
