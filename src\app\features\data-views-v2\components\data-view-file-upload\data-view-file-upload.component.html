<div class="flex justify-between p-4 items-center dialog-modal">
  <h3 mat-dialog-title class="text-lg font-medium">Add Data</h3>
  <button
    mat-icon-button
    class="text-setSettinggray-400 hover:text-gray-600"
    (click)="closeModal()">
    <mat-icon>close</mat-icon>
  </button>
</div>

<hr />

<mat-dialog-content class="max-h-[400px]">
  @if (isLoading) {
    <div
      class="absolute inset-0 flex justify-center items-center bg-gray-500 bg-opacity-50 backdrop-blur-sm z-10">
      <app-loader [loading]="isLoading"></app-loader>
    </div>
  }
  <input
    #fileInput
    type="file"
    multiple
    accept=".csv,.parquet,.jpg,.jpeg,.png"
    class="hidden"
    (change)="onFileInputChange()" />

  <ngx-file-drop
    dropZoneLabel="Drop files or folders here"
    [multiple]="true"
    (onFileDrop)="dropped($event)">
    <ng-template
      ngx-file-drop-content-tmp
      let-openFileSelector="openFileSelector">
      <div class="flex-grow overflow-y-auto content-container">
        @if (!uploadedFilesList().length) {
          <app-upload-placeholder
            [openFileSelector]="openFileSelector"></app-upload-placeholder>
          <!-- Empty state - shown when no files are selected -->
          <div class="text-center p-4 text-gray-500">No files selected</div>
        }

        <!-- Uploaded files list -->
        @if (uploadedFilesList() && uploadedFilesList().length > 0) {
          <div>
            <div class="flex justify-between items-center mb-4">
              <h4 class="text-lg font-medium dark:text-white">
                Uploaded Files ({{
                  uploadedFilesList() ? uploadedFilesList().length : 0
                }})
              </h4>
            </div>

            <!-- File list with table layout -->
            <div class="max-h-[400px] overflow-y-auto rounded p-2">
              <app-upload-files-table
                [uploadedFilesList]="uploadedFilesList()"
                (fileRemoved)="onFileRemoved($event)"></app-upload-files-table>
            </div>
          </div>
          <div class="flex justify-center mt-4">
            <button
              mat-flat-button
              color="primary"
              (click)="openFileSelector()"
              class="py-1">
              <mat-icon class="mr-1">add</mat-icon> Add More Files
            </button>
          </div>
        }
      </div>
    </ng-template>
  </ngx-file-drop>
</mat-dialog-content>

<mat-dialog-actions>
  <!-- Add more files -->
  <div class="flex justify-end space-x-2 p-4">
    <button
      mat-button
      (click)="closeModal()"
      aria-label="Cancel and close modal">
      Cancel
    </button>
    <button
      mat-flat-button
      color="primary"
      [disabled]="!uploadedFilesList().length"
      (click)="uploadFiles()"
      aria-label="Upload selected files">
      Upload
    </button>
  </div>
</mat-dialog-actions>
