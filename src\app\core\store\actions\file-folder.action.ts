import { createAction, props } from '@ngrx/store';
import { FileDirectoryEntry } from '../../../features/data-views-v2/shared/data-view.interfaces';

export const getFileFolderList = createAction(
  '[FileFolder] Get File Folder Hierarchy',
  props<{ projectId: number; path: string }>(),
);

export const getFileFolderSuccess = createAction(
  '[FileFolder] Get File Folder Success',
  props<{ hierarchy: FileDirectoryEntry[]; path: string }>(),
);

export const setLoadedPaths = createAction(
  '[FileFolder] Set Loaded Paths',
  props<{ path: string }>(),
);

export const setloading = createAction(
  '[FileFolder] Set Loaded Paths',
  props<{ isLoading: boolean }>(),
);

export const resetFileState = createAction(
  '[FileFolder] Reset File Hierarchy State',
);
