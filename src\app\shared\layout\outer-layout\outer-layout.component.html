<div class="common-spinner" *ngIf="loaderService.isLoading$ | async">
  <app-loader></app-loader>
</div>

<section
  class="dashboard-wrapper flex h-screen w-screen overflow-hidden mat-app-background">
  <!-- Sidebar Area-->
  <div class="h-full">
    <app-side-bar [page]="currPage"></app-side-bar>
  </div>
  <!-- Main Content Area -->
  <div class="flex flex-col w-full h-full">
    <!-- App Header -->
    <div class="px-1">
      <app-header></app-header>
    </div>

    <!-- Dynamic Outlet -->
    <div class="dynamic-outlet overflow-auto flex-1">
      <router-outlet></router-outlet>
    </div>
  </div>
</section>
