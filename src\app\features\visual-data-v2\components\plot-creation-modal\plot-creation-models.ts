import { FormArray, FormControl, FormGroup } from '@angular/forms';

export type OptionSet = FormGroup<{
  displayValue: FormControl<string>;
  optionName: FormControl<string>;
  optionDescription: FormControl<string>;
  required: FormControl<boolean>;
  multiple: FormControl<boolean>;
  aggregations: FormControl<boolean>;
  canSmartBucketed: FormControl<boolean>;
  selectedSmartBucket: FormControl<SelectedSmartBucket | null>;
  values: FormArray<SelectedOptionValue>;
  datatype: FormControl<string[]>;
}>;

export interface SelectedSmartBucket {
  bucketing_description: string;
  bucketing_type: string;
  smart_bucketing_name: string;
  smart_bucketing_value: string;
}

export type SelectedOptionValue = FormGroup<{
  selectedColName: FormControl<string>;
  selectedArggregation: FormControl<string>;
}>;

export type SettingSetFormGroup = FormGroup<{
  settingName: FormControl<string>;
  selectedSettingValue: FormControl<boolean>;
}>;

export interface FormPlotTypeValue {
  plot_name: string;
  icon: string;
  category: string;
  slug: string;
}

export type PlotCreationForm = FormGroup<{
  plotType: FormControl<FormPlotTypeValue>;
  selectedFileInfo: FormGroup<{
    fileName: FormControl<string>;
    fileId: FormControl<string>;
  }>;
  selectedOptions: FormArray<OptionSet>;
  selectedSettings: FormArray<SettingSetFormGroup>;
}>;

export interface CreatePlotPayload {
  plot_name: string;
  selected_options: OptionSetPayload[];
  selected_settings: SettingsSetPayload[];
  selected_smart_bucketing: BucketingSetPayload[];
}

export interface OptionSetPayload {
  display_value: string;
  option_name: string;
  selected_column_name: string;
  isMultiple: boolean;
  isRequired: boolean;
  columnOptions: string[];
  isChild: boolean;
  selected_smart_bucketing: boolean;
  selected_aggregation?: string;
}

export interface SettingsSetPayload {
  setting_name: string;
  selected_setting_value: boolean;
}

export interface BucketingSetPayload {
  smart_bucketing_name: string;
  smart_bucketing_value: string;
}
