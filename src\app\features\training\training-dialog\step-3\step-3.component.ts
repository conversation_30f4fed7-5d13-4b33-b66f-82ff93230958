/* eslint-disable @typescript-eslint/no-explicit-any */
import { CommonModule } from '@angular/common';
import { Component, effect, inject, Input } from '@angular/core';
import { FormArray, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatLabel } from '@angular/material/form-field';
import { MatSelectChange, MatSelectModule } from '@angular/material/select';
import { TrainingStore } from '../../store/training.store';
import {
  ColumnInfo,
  TargetFeatureInterface,
} from '../../models/training.model';
import { ConfigTypes } from '../../training.const';
import { EmptyStateCardComponent } from '../../../../shared/components/empty-state-card/empty-state-card.component';

@Component({
  selector: 'app-step-3',
  imports: [
    MatExpansionModule,
    MatCheckboxModule,
    ReactiveFormsModule,
    MatLabel,
    MatSelectModule,
    CommonModule,
    EmptyStateCardComponent,
  ],
  templateUrl: './step-3.component.html',
  styleUrl: './step-3.component.css',
})
export class Step3Component {
  @Input() trainingForm!: FormGroup;
  readonly store = inject(TrainingStore);
  featureAndTargets: {
    features: TargetFeatureInterface[];
    targets: TargetFeatureInterface[];
  } = {
    features: [],
    targets: [],
  };
  getConfigArray(type: string): FormArray {
    return this.trainingForm.get(type) as FormArray;
  }
  configTypes = ConfigTypes;

  isfeatureConfig = effect(() => {
    const selectedFile: any = Object.values(
      this.trainingForm.get('assets')?.value,
    ).flat();
    if (this.store.feature_config().length > 0 && selectedFile.length > 0) {
      this.store.getFeaturesTargets(selectedFile[0].file_id);
      if (
        this.featureAndTargets.features.length === 0 &&
        this.featureAndTargets.targets.length === 0
      ) {
        this.featureAndTargets = {
          features: this.store.feature_list(),
          targets: this.store.target_list(),
        };
      }
    }
  });

  onFeatureSelection(event: MatSelectChange) {
    const { value } = event;
    this.featureAndTargets.features.forEach(
      (feature: { name: string; id: number; checked?: boolean }) => {
        feature.checked = value.includes(feature.id);
      },
    );
    this.featureAndTargets.targets = this.store
      .target_list()
      .filter((item: ColumnInfo) => {
        return !value.includes(item.id);
      });
  }

  onTargetSelection(event: MatSelectChange) {
    this.featureAndTargets.features = this.store
      .feature_list()
      .filter((item: ColumnInfo) => {
        return item.id !== event.value;
      });
  }

  isConfigEmpty() {
    return (
      this.getConfigArray('training_config').length === 0 &&
      this.getConfigArray('generation_config').length === 0 &&
      this.getConfigArray('model_config').length === 0 &&
      this.getConfigArray('feature_config').length === 0
    );
  }
}
