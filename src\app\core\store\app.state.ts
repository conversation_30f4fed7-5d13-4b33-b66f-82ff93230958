import { ActionReducerMap } from '@ngrx/store';
import { themeReducer } from './reducers/theme.reducer';
import { ThemeState } from './states/theme.state';
import { tabReducer } from './reducers/tab.reducer';
import { TabState } from './states/tab.state';
import { FileFolderState } from './states/file-folder.state';
import { fileFolderReducer } from './reducers/file-folder.reducer';
import { FileFolderEffects } from './effects/file-folder.effects';

export const appStore: ActionReducerMap<AppState> = {
  theme: themeReducer,
  tab: tabReducer,
  folder: fileFolderReducer,
};

export const AppStoreEffects = [FileFolderEffects];

export interface AppState {
  theme: ThemeState;
  tab: TabState;
  folder: FileFolderState;
}
