<div class="flex flex-row justify-end items-center px-5 h-[80px] space-x-2">
  <!-- Theme Toggle Component -->
  <app-theme-toggle></app-theme-toggle>

  <!-- Notifications Button -->
  <button
    mat-icon-button
    class="text-center rounded-full w-10 h-10 header-icon-button"
    [matMenuTriggerFor]="menu">
    <mat-icon class="icon-accent">notifications_none</mat-icon>
  </button>
  <mat-menu #menu="matMenu" class="w-[400px] h-88">
    <mat-card
      class="w-[400px] rounded-md col-span-12 md:col-span-4 notification-card">
      <mat-card-header class="flex justify-start pt-0 border-b border-subtle">
        <mat-card-title class="text-center w-full py-3 text-primary">
          Fast Actions & Notifications
        </mat-card-title>
      </mat-card-header>
      <mat-card-content>
        @for (notification of notificationLists; track notification) {
          <div
            class="flex items-center my-3 rounded-xl border border-subtle"
            tabindex="0"
            role="button"
            (click)="
              notification.callFunction
                ? notification.callFunction()
                : redirectToLink(notification.link)
            "
            (keyup.enter)="
              notification.callFunction
                ? notification.callFunction()
                : redirectToLink(notification.link)
            ">
            <div class="flex items-center gap-2 px-4 py-5 flex-1">
              <div
                class="w-10 h-10 rounded-full flex items-center justify-center bg-button-primary text-on-primary">
                {{ notification.initial }}
              </div>
              <div class="flex flex-col">
                <p class="text-primary">{{ notification.title }}</p>
                <p class="font-normal text-secondary">
                  {{ notification.message }}
                </p>
              </div>
            </div>
            <div class="flex items-center justify-center w-20 h-20 bg-elevated">
              <img
                src="assets/project/Frame2156.svg"
                alt="Cute illustration"
                class="w-20 h-20 mb-4" />
            </div>
          </div>
        }
      </mat-card-content>
    </mat-card>
  </mat-menu>
</div>
