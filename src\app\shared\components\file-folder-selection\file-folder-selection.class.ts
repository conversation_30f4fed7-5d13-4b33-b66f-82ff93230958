import { inject, Injectable, signal } from '@angular/core';
import { FileDirectoryEntry } from '../../../features/data-views-v2/shared/data-view.interfaces';
import {
  CollectionViewer,
  DataSource,
  SelectionChange,
} from '@angular/cdk/collections';
import { BehaviorSubject, map, merge, Observable } from 'rxjs';
import { FlatTreeControl } from '@angular/cdk/tree';
import { Store } from '@ngrx/store';
import { selectHierarchy } from '../../../core/store/selectors/file-folder.selector';
import { getFileFolderList } from '../../../core/store/actions/file-folder.action';

export class DynamicFlatNode {
  constructor(
    public item: FileDirectoryEntry,
    public level = 1,
    public expandable = false,
    public isLoading = signal(false),
    public isLoadMore = false,
    public checked = false,
  ) {}
}

@Injectable({ providedIn: 'root' })
export class DynamicDatabase {
  // Flat array of all files/folders
  initialData(hierarchyData: FileDirectoryEntry[]): DynamicFlatNode[] {
    const defaultNode: FileDirectoryEntry = {
      name: localStorage.getItem('project_id')?.toString() || '',
      path: '',
      type: 'folder',
      id: 0,
      parentPath: '/',
    };
    let rootNodes: FileDirectoryEntry[] = [];

    if (hierarchyData) {
      rootNodes = hierarchyData.filter(node => {
        return node.path === '' || node.path.split('/').length === 1;
      });
    }
    const allNodes = [defaultNode, ...rootNodes];

    return allNodes.map(
      node => new DynamicFlatNode(node, 0, this.isExpandable(node)),
    );
  }

  /** Returns children of a folder node */
  getChildren(
    node: FileDirectoryEntry,
    hierarchyData: FileDirectoryEntry[],
  ): FileDirectoryEntry[] {
    const parentPath = node.path;
    return hierarchyData.filter(child => {
      if (!child.path.startsWith(parentPath + '/')) return false;

      const relativePath = child.path.slice(parentPath.length + 1); // skip parent/
      return !relativePath.includes('/');
    });
  }

  /** Check if a node is expandable (i.e., a folder) */
  isExpandable(node: FileDirectoryEntry): boolean {
    return node.type === 'folder';
  }
}

export class DynamicDataSource implements DataSource<DynamicFlatNode> {
  dataChange = new BehaviorSubject<DynamicFlatNode[]>([]);
  private store = inject(Store);

  get data(): DynamicFlatNode[] {
    return this.dataChange.value;
  }
  set data(value: DynamicFlatNode[]) {
    this._treeControl.dataNodes = value;
    this.dataChange.next(value);
  }

  constructor(
    private _treeControl: FlatTreeControl<DynamicFlatNode>,
    private _database: DynamicDatabase,
  ) {}

  connect(collectionViewer: CollectionViewer): Observable<DynamicFlatNode[]> {
    this._treeControl.expansionModel.changed.subscribe(change => {
      if (
        (change as SelectionChange<DynamicFlatNode>).added ||
        (change as SelectionChange<DynamicFlatNode>).removed
      ) {
        this.handleTreeControl(change as SelectionChange<DynamicFlatNode>);
      }
    });
    return merge(collectionViewer.viewChange, this.dataChange).pipe(
      map(() => this.data),
    );
  }

  disconnect(): void {
    // to disconnect
  }

  /** Handle expand/collapse behaviors */
  handleTreeControl(change: SelectionChange<DynamicFlatNode>) {
    if (change.added) {
      change.added.forEach(node => this.toggleNode(node, true));
    }
    if (change.removed) {
      change.removed
        .slice()
        .reverse()
        .forEach(node => this.toggleNode(node, false));
    }
  }

  /**
   * Toggle the node, remove from display list
   */
  async toggleNode(node: DynamicFlatNode, expand: boolean) {
    const projectId = localStorage.getItem('project_id');
    node.isLoading.set(true);
    this.store.dispatch(
      getFileFolderList({
        projectId: Number(projectId),
        path: node.item.path,
      }),
    );

    const updatedHierarchy = await new Promise<{
      hierarchy: FileDirectoryEntry[];
      isLoading: boolean;
    }>(resolve => {
      const sub = this.store.select(selectHierarchy).subscribe(state => {
        if (!state.isLoading) {
          sub.unsubscribe();
          resolve(state);
        }
      });
    });
    const children = this._database.getChildren(
      node.item,
      updatedHierarchy.hierarchy,
    );
    const index = this.data.indexOf(node);

    if (!children || index < 0) {
      node.isLoading.set(false);
      return;
    }

    // Step 4: Expand/collapse logic
    if (expand) {
      const nodes = children.map(
        child =>
          new DynamicFlatNode(
            child,
            node.level + 1,
            this._database.isExpandable(child),
          ),
      );
      // Remov the already existing children before insterting the new ones.
      // will replace the logic; as this is not the standard way to do it
      let endIndex = index + 1;
      while (
        endIndex < this.data.length &&
        this.data[endIndex].level > node.level
      ) {
        endIndex++;
      }

      // Remove all existing children at once before adding new ones
      if (endIndex > index + 1) {
        this.data.splice(index + 1, endIndex - (index + 1));
      }
      this.data.splice(index + 1, 0, ...nodes);
    } else {
      let count = 0;
      for (
        let i = index + 1;
        i < this.data.length && this.data[i].level > node.level;
        i++, count++
      ) {
        // counting child nodes to remove
      }
      this.data.splice(index + 1, count);
    }

    // Step 5: Notify and reset loading
    this.dataChange.next(this.data);
    node.isLoading.set(false);
  }
}
