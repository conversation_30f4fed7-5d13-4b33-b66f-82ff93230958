import {
  patchState,
  signalStore,
  withHooks,
  withMethods,
  withState,
} from '@ngrx/signals';
import { inject } from '@angular/core';
import { withDevtools } from '@angular-architects/ngrx-toolkit';
import { TrainingAppState } from './training-app.state';
import { TrainingService } from '../service/training-service.service';
import { ApiInferenceService } from '../service/api-inference.service';
import { DlInferenceCodeSnippetBodyPayload } from '../models/api-inference-code-snippet.model';
import { DlInferenceInstance } from '../models/api-inference-deployment.model';

const initialState: TrainingAppState = {
  trainingInfo: {
    id: null,
  },
  deployedInference: {
    instances: [],
  },
};

export const TrainingAppStore = signalStore(
  { providedIn: 'root' },
  withState<TrainingAppState>(initialState),

  // DevTools integration
  withDevtools('TrainingAppStore'),

  // For Store change
  withMethods(store => ({
    _setTrainingID(trainingId: string) {
      patchState(store, {
        trainingInfo: {
          id: trainingId,
        },
      });
    },

    _setDlInferenceJobs(instances: DlInferenceInstance[]) {
      patchState(store, {
        deployedInference: {
          instances,
        },
      });
    },
  })),

  // For DL Deployments
  withMethods((store, apiInferenceService = inject(ApiInferenceService)) => ({
    async startDlInferenceInstanceDeploymentProcess(
      dlTrainingId: string,
      payload: DlInferenceCodeSnippetBodyPayload,
    ): Promise<void> {
      try {
        await apiInferenceService.starDlInferenceInstance(
          dlTrainingId,
          payload,
        );
      } catch (err) {
        console.error('Error While Starting the DL Inference', err);
        throw err;
      }
    },

    async stopDlInferenceInstanceDeploymentProcess(
      dlInferenceInstanceId: string,
    ): Promise<void> {
      try {
        await apiInferenceService.stopDlInferenceInstance(
          dlInferenceInstanceId,
        );
      } catch (err) {
        console.error('Error While Stopping the DL Inference', err);
      }
    },

    async getDlInferenceJobs(dlTrainingId: string): Promise<void> {
      try {
        const res =
          await apiInferenceService.getApiDlInferenceInstances(dlTrainingId);
        if (res.data) {
          const dlInferenceJobs = res.data;
          store._setDlInferenceJobs(dlInferenceJobs);
        } else {
          store._setDlInferenceJobs([]);
        }
      } catch (err) {
        console.error('Error While Starting the DL Inference', err);
      }
    },
  })),

  // Hooks
  withHooks({
    onInit: async store => {
      // LOAD PROJECT DATA
      const trainingService = inject(TrainingService);
      const trainingId = trainingService.getTrainingIdFromUrl();
      if (trainingId) {
        store._setTrainingID(trainingId);
        console.log('Training ID from URL:', trainingId);
      } else {
        console.error('Error in setting the training ID at Store');
      }
      console.log('Initial state:', store);
    },
  }),
);
