<div>
  <!-- Header -->
  <div class="flex flex-row justify-between">
    <div class="flex flex-row gap-2 p-2">
      @for (snippet of this.snippets(); track snippet) {
        <button
          class="px-4 py-2 rounded-md transition-colors duration-200 font-medium text-sm text-gray-700"
          [ngClass]="{
            'bg-[#6750A41F]': currentSelection()?.language === snippet.language,
            'bg-transparent': currentSelection()?.language !== snippet.language,
          }"
          (click)="currentSelection.set(snippet)">
          {{ snippet.label }}
        </button>
      }
    </div>
    <div class="flex justify-center items-center p-2">
      <button (click)="copySnippet()">
        <mat-icon>content_copy</mat-icon>
      </button>
    </div>
  </div>
  <!-- Content -->
  <div>
    @if (currentSelection()) {
      <app-code-snippet
        [code]="currentSelection()?.snippet ?? ''"
        [language]="currentSelection()?.language ?? 'bash'"></app-code-snippet>
    } @else {
      <p class="text-gray-500">Select a snippet to view its code.</p>
    }
  </div>
</div>
