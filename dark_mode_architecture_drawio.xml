<?xml version="1.0" encoding="UTF-8"?>
<mxfile host="app.diagrams.net" modified="2024-01-01T00:00:00.000Z" agent="Draw.io" etag="xxx" version="22.1.16" type="device">
  <diagram name="Dark Mode Architecture" id="dark-mode-arch">
    <mxGraphModel dx="1422" dy="794" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1169" pageHeight="827" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        
        <!-- Title -->
        <mxCell id="title" value="Dark Mode Architecture - Frontend v2" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=20;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="400" y="20" width="360" height="30" as="geometry" />
        </mxCell>
        
        <!-- User Interaction Layer -->
        <mxCell id="user" value="👤 User" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1f5fe;strokeColor=#01579b;" vertex="1" parent="1">
          <mxGeometry x="80" y="80" width="120" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="toggle" value="🌙 Theme Toggle&#xa;Component" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1f5fe;strokeColor=#01579b;" vertex="1" parent="1">
          <mxGeometry x="280" y="80" width="120" height="60" as="geometry" />
        </mxCell>
        
        <!-- Service Layer -->
        <mxCell id="themeService" value="🔧 Theme Service" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#4a148c;" vertex="1" parent="1">
          <mxGeometry x="480" y="80" width="120" height="60" as="geometry" />
        </mxCell>
        
        <!-- State Management Layer -->
        <mxCell id="store" value="🗄️ NgRx Store" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#2e7d32;" vertex="1" parent="1">
          <mxGeometry x="680" y="80" width="120" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="reducer" value="⚙️ Theme Reducer" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#2e7d32;" vertex="1" parent="1">
          <mxGeometry x="880" y="80" width="120" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="selector" value="🎯 Theme Selector" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#2e7d32;" vertex="1" parent="1">
          <mxGeometry x="880" y="180" width="120" height="60" as="geometry" />
        </mxCell>
        
        <!-- Persistence Layer -->
        <mxCell id="localStorage" value="💾 localStorage" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#2e7d32;" vertex="1" parent="1">
          <mxGeometry x="480" y="180" width="120" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="systemPref" value="🖥️ System&#xa;Preference" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#2e7d32;" vertex="1" parent="1">
          <mxGeometry x="480" y="280" width="120" height="60" as="geometry" />
        </mxCell>
        
        <!-- DOM Layer -->
        <mxCell id="dom" value="🌐 DOM Body Classes" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#2e7d32;" vertex="1" parent="1">
          <mxGeometry x="280" y="180" width="120" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="lightMode" value=".light-mode" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#2e7d32;" vertex="1" parent="1">
          <mxGeometry x="80" y="280" width="120" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="darkMode" value=".dark-mode" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#2e7d32;" vertex="1" parent="1">
          <mxGeometry x="280" y="280" width="120" height="60" as="geometry" />
        </mxCell>
        
        <!-- CSS Theming Architecture Group -->
        <mxCell id="cssGroup" value="CSS Theming Architecture" style="swimlane;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#e65100;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="80" y="380" width="520" height="280" as="geometry" />
        </mxCell>
        
        <!-- Angular Material Components Subgroup -->
        <mxCell id="materialGroup" value="Angular Material Components" style="swimlane;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#e65100;" vertex="1" parent="cssGroup">
          <mxGeometry x="20" y="40" width="220" height="220" as="geometry" />
        </mxCell>
        
        <mxCell id="materialTheme" value="📦 Angular Material&#xa;Theming API" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#e65100;" vertex="1" parent="materialGroup">
          <mxGeometry x="50" y="40" width="120" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="lightTheme" value="☀️ Light Theme&#xa;Mixins" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#e65100;" vertex="1" parent="materialGroup">
          <mxGeometry x="10" y="140" width="90" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="darkTheme" value="🌙 Dark Theme&#xa;Mixins" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#e65100;" vertex="1" parent="materialGroup">
          <mxGeometry x="120" y="140" width="90" height="60" as="geometry" />
        </mxCell>
        
        <!-- Custom Components Subgroup -->
        <mxCell id="customGroup" value="Custom Components" style="swimlane;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#e65100;" vertex="1" parent="cssGroup">
          <mxGeometry x="260" y="40" width="240" height="220" as="geometry" />
        </mxCell>
        
        <mxCell id="cssVars" value="🎨 CSS Custom&#xa;Properties" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#e65100;" vertex="1" parent="customGroup">
          <mxGeometry x="60" y="40" width="120" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="utilityClasses" value="🛠️ Utility Classes" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#e65100;" vertex="1" parent="customGroup">
          <mxGeometry x="10" y="140" width="100" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="tailwindDark" value="🎯 Tailwind&#xa;Dark Mode" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#e65100;" vertex="1" parent="customGroup">
          <mxGeometry x="130" y="140" width="100" height="60" as="geometry" />
        </mxCell>
        
        <!-- Component Updates Group -->
        <mxCell id="componentGroup" value="Component Updates" style="swimlane;whiteSpace=wrap;html=1;fillColor=#fce4ec;strokeColor=#880e4f;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="680" y="380" width="320" height="180" as="geometry" />
        </mxCell>
        
        <mxCell id="componentA" value="📱 Component A" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fce4ec;strokeColor=#880e4f;" vertex="1" parent="componentGroup">
          <mxGeometry x="20" y="40" width="80" height="50" as="geometry" />
        </mxCell>
        
        <mxCell id="componentB" value="📱 Component B" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fce4ec;strokeColor=#880e4f;" vertex="1" parent="componentGroup">
          <mxGeometry x="120" y="40" width="80" height="50" as="geometry" />
        </mxCell>
        
        <mxCell id="componentC" value="📱 Component C" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fce4ec;strokeColor=#880e4f;" vertex="1" parent="componentGroup">
          <mxGeometry x="220" y="40" width="80" height="50" as="geometry" />
        </mxCell>
        
        <mxCell id="themeObs" value="currentTheme$&#xa;Observable" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fce4ec;strokeColor=#880e4f;" vertex="1" parent="componentGroup">
          <mxGeometry x="110" y="110" width="100" height="50" as="geometry" />
        </mxCell>
        
        <!-- App Initialization Group -->
        <mxCell id="initGroup" value="App Initialization" style="swimlane;whiteSpace=wrap;html=1;fillColor=#f1f8e9;strokeColor=#33691e;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="680" y="280" width="200" height="80" as="geometry" />
        </mxCell>
        
        <mxCell id="appConfig" value="⚙️ app.config.ts" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f1f8e9;strokeColor=#33691e;" vertex="1" parent="initGroup">
          <mxGeometry x="20" y="30" width="80" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="themeInit" value="🚀 THEME_INITIALIZER" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f1f8e9;strokeColor=#33691e;" vertex="1" parent="initGroup">
          <mxGeometry x="110" y="30" width="80" height="40" as="geometry" />
        </mxCell>
        
        <!-- Arrows -->
        <mxCell id="arrow1" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="user" target="toggle">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="arrow2" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="toggle" target="themeService">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="arrow3" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="themeService" target="store">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="arrow4" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="store" target="reducer">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="arrow5" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="reducer" target="selector">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="arrow6" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="themeService" target="localStorage">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="arrow7" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="localStorage" target="systemPref">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="arrow8" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="themeService" target="dom">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="arrow9" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="dom" target="lightMode">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="arrow10" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="dom" target="darkMode">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- Internal CSS Group Arrows -->
        <mxCell id="arrow11" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="materialGroup" source="materialTheme" target="lightTheme">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="arrow12" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="materialGroup" source="materialTheme" target="darkTheme">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="arrow13" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="customGroup" source="cssVars" target="utilityClasses">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="arrow14" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="customGroup" source="cssVars" target="tailwindDark">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- Component Group Arrows -->
        <mxCell id="arrow15" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="componentGroup" source="componentA" target="themeObs">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="arrow16" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="componentGroup" source="componentB" target="themeObs">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="arrow17" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="componentGroup" source="componentC" target="themeObs">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- Init Group Arrow -->
        <mxCell id="arrow18" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="initGroup" source="appConfig" target="themeInit">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- Cross-group connections -->
        <mxCell id="arrow19" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="themeInit" target="themeService">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="770" y="360" />
              <mxPoint x="770" y="360" />
              <mxPoint x="540" y="360" />
              <mxPoint x="540" y="80" />
            </Array>
          </mxGeometry>
        </mxCell>
        
        <mxCell id="arrow20" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="selector" target="themeObs">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="850" y="210" />
              <mxPoint x="850" y="535" />
            </Array>
          </mxGeometry>
        </mxCell>
        
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
