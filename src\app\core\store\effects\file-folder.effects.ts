import { inject, Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import {
  getFileFolderList,
  getFileFolderSuccess,
} from '../actions/file-folder.action';
import { map, mergeMap, withLatestFrom } from 'rxjs';
import { DataViewServiceV2 } from '../../../features/data-views-v2/services/data-view.service';
import { FileDirectoryEntry } from '../../../features/data-views-v2/shared/data-view.interfaces';
import { Store } from '@ngrx/store';
import { FileFolderState } from '../states/file-folder.state';
import { selectHierarchy } from '../selectors/file-folder.selector';
import { updateHierarchy } from '../../../features/data-views-v2/shared/files.methods';

@Injectable()
export class FileFolderEffects {
  private actions$ = inject(Actions);
  private dataViewService = inject(DataViewServiceV2);
  private store = inject(Store<FileFolderState>);

  getFileFolderList$ = createEffect(() =>
    this.actions$.pipe(
      ofType(getFileFolderList),
      mergeMap(({ projectId, path }) =>
        this.dataViewService
          .getFilesList(
            projectId,
            1,
            10,
            path.startsWith('/') ? path.slice(1) : path,
            { type: '', value: '' },
            '',
            true,
          )
          .pipe(
            withLatestFrom(this.store.select(selectHierarchy)),
            map(([files, currentHierarchy]) => {
              const hierarchy: FileDirectoryEntry[] = [];
              // Add files
              files.data.files.forEach(
                (file: { name: string; id: number; folder: string }) => {
                  const parentPath = path || '/';
                  hierarchy.push({
                    name: file.name,
                    path: `${parentPath === '/' ? '' : parentPath}/${file.name}`,
                    type: 'file',
                    id: file.id,
                    parentPath: parentPath,
                  });
                },
              );

              // Add subfolders
              files.data.subfolders.forEach((folderName: string) => {
                const parentPath = path || '/';
                hierarchy.push({
                  name: folderName,
                  path: `${parentPath === '/' ? '' : parentPath}/${folderName}`,
                  type: 'folder',
                  id: 0,
                  parentPath: parentPath,
                });
              });

              const finalHierarchy =
                currentHierarchy.hierarchy.length > 0
                  ? updateHierarchy(
                      currentHierarchy.hierarchy,
                      `${path}`,
                      hierarchy,
                    )
                  : hierarchy;
              const uniqueFinalHierarchy = Array.from(
                new Map(finalHierarchy.map(item => [item.path, item])).values(),
              );

              return getFileFolderSuccess({
                hierarchy: uniqueFinalHierarchy,
                path: path,
              });
            }),
          ),
      ),
    ),
  );
}
