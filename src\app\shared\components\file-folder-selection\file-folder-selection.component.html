<button
  class="dropdown-container w-full"
  (click)="toggleDropdown()"
  tabindex="0"
  #dropdownContainer>
  <div class="dropdown-trigger">
    <span>{{ selectedLabel || 'Select Files' }}</span>
    <mat-icon class="dropdown-arrow">{{
      isDropdownOpen ? 'expand_less' : 'expand_more'
    }}</mat-icon>
  </div>

  <div class="dropdown-panel dark:bg-[#181C20]" *ngIf="isDropdownOpen">
    <mat-tree
      [dataSource]="dataSource"
      [treeControl]="treeControl"
      class="file-tree">
      <!-- File Node -->
      <mat-tree-node
        *matTreeNodeDef="let node"
        matTreeNodePadding
        class="file-node relative dark:hover:bg-[#212529]">
        <mat-icon class="file-icon">{{ getFileIcon(node.item.name) }}</mat-icon>
        <span class="node-label">{{ node.item.name }}</span>
        @if (selectionType === 'file') {
          <mat-checkbox
            [checked]="isNodeChecked(node)"
            (click)="$event.stopPropagation()"
            (change)="isSelected(node, $event)"
            class="absolute right-0">
          </mat-checkbox>
        }
      </mat-tree-node>

      <!-- Folder Node -->
      <mat-tree-node
        *matTreeNodeDef="let node; when: hasChild"
        matTreeNodePadding
        matTreeNodeToggle
        class="relative dark:hover:bg-[#212529]">
        <mat-icon class="folder-icon">
          {{ treeControl.isExpanded(node) ? 'folder_open' : 'folder' }}
        </mat-icon>
        <span class="node-label">{{ node.item.name }}</span>
        @if (selectionType === 'folder') {
          <mat-checkbox
            [checked]="isNodeChecked(node)"
            (click)="$event.stopPropagation()"
            (change)="isSelected(node, $event)"
            class="absolute right-0">
          </mat-checkbox>
        }
      </mat-tree-node>
    </mat-tree>
  </div>
</button>
