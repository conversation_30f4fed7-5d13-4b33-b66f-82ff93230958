import { components } from '../../schema/schema';

//TODO POST visualization/user-plot/
//TODO POST visualization/user-plot/${plot_id}/associate-filters/
//TODO POST visualization/UserPlot/${plot_id}/description/
export type UserPlotRequest = components['schemas']['UserPlotRequest'];

//TODO PATCH visualization/user-plot/display-layout/
export type PatchedUserPlotRequest =
  components['schemas']['PatchedUserPlotRequest'];

export type PlotStyle = components['schemas']['PlotStyle'];

export type Plot = components['schemas']['Plot'];

//TODO GET visualization/Plot/
export interface PlotTypesResponse {
  Visualise: PlotType[];
  Explain: PlotType[];
  Standard: PlotType[];
  CompleteList: PlotType[];
}

export interface PlotType {
  plot_name: string;
  icon: string;
  category: string;
}

//TODO GET visualization/Plot/${plot_name}/
export interface PlotInfoResponse {
  plot_name: string;
  plot_description: string;
  icon: string;
  category: string;
  option_set: OptionSet[];
  setting_set: SettingSet[];
  aggregation_set: AggregationSet[];
  smartbucketing_set: SmartbucketingSet[];
  slug: string;
}

export interface OptionSet {
  display_value: string;
  option_name: string;
  option_description: string;
  required: boolean;
  multiple: boolean;
  aggregations: boolean;
  smart_bucketing: boolean;
  datatype: string[];
}

export interface SettingSet {
  setting_name: string;
  setting_description: string;
  multiple: boolean;
  settings_type: 'Switch_buttons' | 'Dropdown_Settings';
}

export interface AggregationSet {
  aggregation_type: string;
  aggregation_description: string;
}

export interface SmartbucketingSet {
  smart_bucketing_name: string;
  smart_bucketing_value: string;
  bucketing_type: string;
  bucketing_description: string;
}

//TODO POST visualization/UserPlot/${plot_id}/favorite/
export interface SetFavoritePlotResponse {
  plot_id: number;
  favorite: boolean;
}

//File
export interface File {
  file_id: number;
  file_name: string;
  created_date: string;
}

export interface Subfolder {
  folder_id: number;
  folder_name: string;
  subfolders: Subfolder[];
  files: File[];
}

//TODO GET projects/projects/${project_id}/files/filter/
//TODO GET files/folders/hierarchy/${folder_id}/
export interface Folder {
  folder_id: number;
  folder_name: string;
  subfolders: Subfolder[];
  files: File[];
}
