import {
  Component,
  WritableSignal,
  computed,
  effect,
  signal,
  Signal,
} from '@angular/core';
import { CodeSnippetMeta } from '../../models/api-inference-code-snippet.model';
import { CodeSnippetComponent } from '../code-snippet/code-snippet.component';
import { input } from '@angular/core';
import { NgClass } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { Clipboard, ClipboardModule } from '@angular/cdk/clipboard';

@Component({
  selector: 'app-multi-lang-snippet-container',
  imports: [
    CodeSnippetComponent,
    MatButtonModule,
    MatIconModule,
    ClipboardModule,
    NgClass,
  ],
  templateUrl: './multi-lang-snippet-container.component.html',
  styleUrl: './multi-lang-snippet-container.component.css',
})
export class MultiLangSnippetContainerComponent {
  readonly snippets = input<CodeSnippetMeta[]>();

  currentSelection: WritableSignal<CodeSnippetMeta | null> = signal(null);

  languageOptions: Signal<string[]> = computed(
    () => this.snippets()?.map(snippet => snippet.language) ?? [],
  );

  constructor(private clipboard: Clipboard) {
    effect(() => {
      const snippets = this.snippets();
      if (snippets && snippets.length > 0) {
        this.currentSelection.set(snippets[0]);
      }
    });
  }

  copySnippet(): void {
    const success = this.clipboard.copy(this.currentSelection()?.snippet || '');

    if (success) {
      console.log('Copied to clipboard');
    } else {
      console.warn('Copy failed');
    }
  }
}
