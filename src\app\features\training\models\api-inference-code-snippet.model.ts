// START : API Section : {{base_url}}/dlmodels/api/v1/inference-snippets/{{dl_training_id}}/start

export interface CodeSnippetResponseData {
  results: CodeSnippetMeta[];
}

export interface CodeSnippetMeta {
  language: string;
  snippet: string;
  label: string;
}

export interface DlInferenceCodeSnippetBodyPayload {
  name: string;
  compute_type: string; // CPU or GPU
  shutdown_type: string; //manual  or auto
  timeout: string; // only when shutdown_type=auto. timeout in mins of non activity
  notes: string;
}
// END : API Section
