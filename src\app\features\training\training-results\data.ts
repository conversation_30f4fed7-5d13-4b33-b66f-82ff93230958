// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const ModelOverviewData = (resultData: any) => {
  const displayData = [
    {
      name: 'Model Name',
      value: resultData?.model?.name ?? '',
    },
    {
      name: 'Training Status',
      value: resultData?.dl_training_status,
    },
    { name: 'Training Duration', value: resultData?.training_time },
  ];
  const displayColumns = ['name', 'value'];
  return { displayData: displayData, displayColumns: displayColumns };
};

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const Metric = (resultData: any) => {
  const displayData = Object.entries(resultData.results.metrics)
    .filter(([, value]) => typeof value !== 'object' || value === null)
    .map(([key, value]) => {
      const safeValue = value as string | number;
      return {
        metric: formatKey(key),
        value: formatValue(safeValue),
      };
    });

  const displayColumns = ['metric', 'value'];
  return { displayData: displayData, displayColumns: displayColumns };
};

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const ClassificationReport = (reportData: any) => {
  const displayData = Object.entries(
    reportData.results.metrics.classification_report,
  )
    .filter(([, value]) => typeof value === 'object' && value !== null)
    .map(([key, value]) => {
      const className = formatKey(key);

      // Add runtime check to satisfy TS and ensure safety
      if (typeof value !== 'object' || value === null)
        return { class: className };

      const metrics = Object.entries(value as Record<string, unknown>).reduce(
        (acc, [metricKey, metricValue]) => {
          if (
            typeof metricValue === 'string' ||
            typeof metricValue === 'number'
          ) {
            acc[metricKey] = formatValue(metricValue);
          }
          return acc;
        },
        {} as Record<string, string>,
      );

      return {
        class: className,
        ...metrics,
      };
    });

  const displayColumns = [
    'class',
    ...new Set(
      displayData.flatMap(row => Object.keys(row)).filter(k => k !== 'class'),
    ),
  ];
  console.log(displayData, 'displaydata');

  return { displayData, displayColumns };
};

function formatValue(num: string | number) {
  return typeof num === 'number' ? num.toFixed(2) : num;
}

function formatKey(type: string) {
  return type
    .split('_') // Split by underscore
    .map(word => capitalize(word)) // Capitalize each word
    .join(' '); // Join with spaces
}

function capitalize(str: string) {
  return str.charAt(0).toUpperCase() + str.slice(1);
}
