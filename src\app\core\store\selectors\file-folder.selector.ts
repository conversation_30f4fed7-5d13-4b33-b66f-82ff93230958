import { createFeatureSelector, createSelector } from '@ngrx/store';
import { FileFolderState } from '../states/file-folder.state';

export const fileFolderState = createFeatureSelector<FileFolderState>('folder');

export const selectHierarchy = createSelector(
  fileFolderState,
  state => state.folder,
);

export const selectLoadedPaths = createSelector(
  fileFolderState,
  state => state.folder.loadedPaths,
);
