@use '@angular/material' as mat;
@use '../m3-theme.scss';
@use './styles/color.scss';
@use './styles/utilities.scss';

@include mat.core();

$theme: m3-theme.$light-theme;
$dark-theme: m3-theme.$dark-theme;

// ========================================
// ANGULAR THEMING API - AUTOMATIC MATERIAL COMPONENT THEMING
// ========================================

// Default light theme for all Material components
:root {
  @include mat.all-component-themes($theme);
  @include mat.color-variants-backwards-compatibility($theme);
  @include mat.select-overrides(
    (
      trigger-text-line-height: 1.8rem,
    )
  );
}

// Dark theme for all Material components
.dark-mode {
  @include mat.all-component-colors($dark-theme);
  @include mat.color-variants-backwards-compatibility($dark-theme);
}

// ========================================
// GLOBAL STYLES (THEME-INDEPENDENT)
// ========================================

a {
  color: var(--md-sys-color-primary) !important;
  cursor: pointer;
}

html,
body {
  font-family: var(--md-sys-typescale-body-medium-font);
  font-size: var(--md-sys-typescale-body-medium-size);
  margin: 0;
  padding: 0;
  height: 100%;
  width: 100%;
  box-sizing: border-box;
}

/* Ensure all elements inherit box-sizing */
*,
*::before,
*::after {
  box-sizing: inherit;
}

label,
span {
  font-size: var(--md-sys-typescale-title-small-size);
  font-weight: var(--md-sys-typescale-title-small-weight);
}

h1 {
  font-size: var(--md-sys-typescale-headline-large-size) !important;
  font-weight: var(--md-sys-typescale-title-small-weight) !important;
}

h2 {
  font-size: var(--md-sys-typescale-headline-medium-size) !important;
  font-weight: var(--md-sys-typescale-title-small-weight) !important;
}

h3 {
  font-size: var(--md-sys-typescale-headline-small-size) !important;
  font-weight: var(--md-sys-typescale-headline-small-weight) !important;
}

mat-card-title {
  font-size: var(--md-sys-typescale-title-medium-size) !important;
  font-weight: var(--md-sys-typescale-title-medium-weight) !important;
}

form label {
  font-size: var(--md-sys-typescale-title-medium-size);
}

form input,
textarea {
  font-size: var(--md-sys-typescale-title-small-size);
}

a {
  color: var(--md-sys-color-primary) !important;
  cursor: pointer;
}

.mat-mdc-tooltip-surface {
  width: 400px !important;
  height: auto;
  max-height: 200px;
  min-height: 200px;
  min-width: auto !important;
  max-width: unset !important;
}

// ========================================
// COMPONENT-SPECIFIC OVERRIDES
// Only for cases where Angular Material theming needs specific adjustments
// ========================================

.light-mode {
  mat-card {
    background: #fff;
  }

  .mat-search {
    background: #fff;
  }
}

.stroke-btn {
  background-color: white !important;
}

.default-filter {
  border: 1px solid #dbdfea;
}

.welcome {
  font-size: 3.25rem !important;
  line-height: 2.5rem !important;
}

// These are specific customizations on top of Angular theming for design requirements
.mat-dialog input,
.mat-dialog textarea,
.mat-dialog select {
  background: var(--md-sys-color-surface);
}

.mat-button-toggle-checked {
  background-color: var(--md-sys-color-primary) !important;
  color: var(--md-sys-color-on-primary) !important;
}

// Table styling - theme-aware
#light-table td,
th,
#light-data-table td {
  text-align: center;
  border-right: 1px solid #e5e5f4;
  border: 1px solid #ecebf8;
}

#light-table td:last-child,
th:last-child,
#light-data-table td:last-child,
#light-data-table th:last-child {
  border-right: none;
}

#light-table table tr:nth-child(odd),
#light-data-table table tr:nth-child(odd) {
  background-color: #f1f3f9;
}

// Dark mode table stripes - subtle contrast
.dark-mode {
  #light-table table tr:nth-child(odd),
  #light-data-table table tr:nth-child(odd) {
    background-color: #1a1e23 !important;
  }

  #light-table table tr:nth-child(even),
  #light-data-table table tr:nth-child(even) {
    background-color: #1f2328 !important;
  }
}

#light-table table th,
#light-data-table table th {
  background-color: var(--md-sys-color-surface);
}

#light-table td,
#light-data-table td {
  color: #82868e;
}

// ========================================
// LEGACY OVERRIDES - TO BE MIGRATED TO UTILITY CLASSES
// These should gradually be replaced with semantic utility classes
// ========================================
.dark-mode {
  mat-card,
  .mat-dialog,
  .mat-search,
  mat-drawer {
    background: #181c20 !important;
  }

  .mat-mdc-card-title {
    color: white !important;
  }

  .stroke-btn,
  .default-filter {
    background-color: var(--mat-select-panel-background-color);
  }

  .text-gray-600,
  .text-gray-500,
  .text-gray-800 {
    color: var(--md-sys-color-surface-light) !important;
  }

  .initial-text {
    color: black;
  }

  mat-calendar {
    background: #54585f !important;
  }
}

.initial-text {
  color: var(--md-sys-color-on-surface);
}

ng-multiselect-dropdown .dropdown-btn {
  height: 3rem;
  color: #969799;
  padding: 12px !important;
  border: 1px solid #e5e7eb !important;
  box-shadow:
    var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000),
    var(--tw-shadow) !important;
  background-color: transparent !important;
}

.ngx-file-drop__drop-zone {
  border: none !important;
  border-radius: 0 !important;
  height: 100% !important;
}
.ngx-file-drop__content {
  height: inherit !important;
}

.ng-dropdown-panel-items.scroll-host {
  margin-top: 10px;
  padding: 12px;
}
.ng-option {
  padding: 12px;
}
.mat-expansion-panel-modal .mat-expansion-panel-body {
  height: 340px;
  overflow-y: auto;
  padding: 0 24px 86px;
}

.mat-expansion-panel {
  background: transparent !important;
}

.mat-mdc-option .mdc-list-item__primary-text {
  display: inline-flex;
}

.mat-mdc-text-field-wrapper {
  height: 48px !important;
}
.mat-button-toggle-label-content {
  display: inline-flex !important;
  padding-top: 3px !important;
}
.mat-pseudo-checkbox {
  display: none !important;
}

.mat-button-toggle-appearance-standard.mat-button-toggle-checked {
  color: white !important;
}

.grid-bg-custom {
  background: rgb(32, 61, 84);
  background: linear-gradient(
    105deg,
    rgba(32, 61, 84, 1) 0%,
    rgba(27, 89, 110, 1) 100%
  );
  border-bottom-right-radius: 200px;
}
.upper-left {
  background: linear-gradient(
    40.24deg,
    rgba(0, 249, 255, 0.5) 14.22%,
    rgba(32, 61, 84, 0.5) 38.73%
  );
}
.upper-right {
  background: linear-gradient(
    211.95deg,
    rgba(1, 121, 193, 0.5) 14.91%,
    rgba(32, 61, 84, 0.5) 38.64%
  );
}

/* gridster overrides */
gridster {
  .gridster-row {
    border-top: 1px solid var(--md-sys-color-outline) !important;
    border-bottom: 1px solid var(--md-sys-color-outline) !important;
  }

  .gridster-column {
    border-left: 1px solid var(--md-sys-color-outline) !important;
    border-right: 1px solid var(--md-sys-color-outline) !important;
  }

  background: var(--md-sys-color-surface-bright) !important;

  //gridster-preview {
  //  background: rgba(0, 0, 0, 0.25) !important;
  //}
}

.active-chip {
  background-color: var(--md-sys-color-primary) !important;

  .mdc-evolution-chip__text-label {
    color: var(--md-sys-color-on-primary) !important;
    font-weight: bold;
  }
}

:root {
  --mat-option-selected-state-layer-color: #dbdbdb;
  --mat-option-selected-state-label-text-color: #191c20;
}